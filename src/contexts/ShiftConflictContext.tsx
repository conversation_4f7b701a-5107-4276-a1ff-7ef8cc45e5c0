import React, { createContext, useContext, useReducer, ReactNode, useCallback, useMemo } from 'react'
import { AttendanceShift } from 'types/attendance'

export interface ConflictData {
  id: string
  date: string
  employeeId: string
  shiftKey: string
  shift: AttendanceShift & {
    neverClockedOut?: boolean
    scheduledShift?: any
    missingPosition?: boolean
    isClockInDifferent?: boolean
    isClockOutDifferent?: boolean
    shiftStartRounded?: number
    shiftEndRounded?: number
  }
  type?: 'clock_in_different' | 'clock_out_different' | 'never_clocked_out' | 'missing_position'
  isResolved?: boolean
}

export interface ConflictResolution {
  type: 'approve' | 'reject' | 'modify' | 'delete'
  modifiedShift?: AttendanceShift
  modifiedData?: any
  // Additional data needed for Firebase operations
  employeeId?: string
  date?: string
  shiftKey?: string
  shouldPersist?: boolean // Whether to save to Firebase (default: true)
}

interface ConflictState {
  conflicts: ConflictData[]
  isLoading: boolean
  selectedConflicts: string[]
  resolvedConflicts: string[]
  resolutionInProgress: Set<string>
}

type ConflictAction =
  | { type: 'SET_CONFLICTS'; payload: ConflictData[] }
  | { type: 'RESOLVE_CONFLICT'; payload: { conflictId: string; resolution: ConflictResolution } }
  | { type: 'UNDO_RESOLVE'; payload: string }
  | { type: 'SELECT_CONFLICTS'; payload: string[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_RESOLUTION_IN_PROGRESS'; payload: { conflictId: string; inProgress: boolean } }
  | { type: 'REFRESH_CONFLICTS' }

const initialState: ConflictState = {
  conflicts: [],
  isLoading: false,
  selectedConflicts: [],
  resolvedConflicts: [],
  resolutionInProgress: new Set()
}

const conflictReducer = (state: ConflictState, action: ConflictAction): ConflictState => {
  switch (action.type) {
    case 'SET_CONFLICTS':
      return {
        ...state,
        conflicts: action.payload,
        // Clear resolved conflicts that are no longer in the conflicts array
        resolvedConflicts: state.resolvedConflicts.filter(id =>
          action.payload.some(conflict => conflict.id === id)
        )
      }

    case 'RESOLVE_CONFLICT':
      const { conflictId } = action.payload
      return {
        ...state,
        resolvedConflicts: [...state.resolvedConflicts, conflictId],
        conflicts: state.conflicts.map(conflict =>
          conflict.id === conflictId
            ? { ...conflict, isResolved: true }
            : conflict
        ),
        resolutionInProgress: new Set([...state.resolutionInProgress].filter(id => id !== conflictId))
      }

    case 'UNDO_RESOLVE':
      return {
        ...state,
        resolvedConflicts: state.resolvedConflicts.filter(id => id !== action.payload),
        conflicts: state.conflicts.map(conflict =>
          conflict.id === action.payload
            ? { ...conflict, isResolved: false }
            : conflict
        )
      }

    case 'SELECT_CONFLICTS':
      return {
        ...state,
        selectedConflicts: action.payload
      }

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      }

    case 'SET_RESOLUTION_IN_PROGRESS':
      const newInProgress = new Set(state.resolutionInProgress)
      if (action.payload.inProgress) {
        newInProgress.add(action.payload.conflictId)
      } else {
        newInProgress.delete(action.payload.conflictId)
      }
      return {
        ...state,
        resolutionInProgress: newInProgress
      }

    case 'REFRESH_CONFLICTS':
      return {
        ...state,
        resolvedConflicts: [],
        selectedConflicts: [],
        resolutionInProgress: new Set()
      }

    default:
      return state
  }
}

interface ShiftConflictContextType {
  state: ConflictState
  dispatch: React.Dispatch<ConflictAction>
  actions: {
    setConflicts: (conflicts: ConflictData[]) => void
    resolveConflict: (conflictId: string, resolution: ConflictResolution) => Promise<void>
    undoResolve: (conflictId: string) => void
    selectConflicts: (conflictIds: string[]) => void
    setLoading: (loading: boolean) => void
    refreshConflicts: () => void
    isConflictResolved: (conflictId: string) => boolean
    isResolutionInProgress: (conflictId: string) => boolean
    getUnresolvedConflicts: () => ConflictData[]
    getConflictsByType: () => Record<string, ConflictData[]>
  }
}

const ShiftConflictContext = createContext<ShiftConflictContextType | null>(null)

export interface ShiftConflictProviderProps {
  children: ReactNode
  onResolveConflictCallback?: (conflictId: string, resolution: ConflictResolution) => Promise<void>
  // Firebase operation callbacks
  onSaveShift?: (newShift: { [key: string]: AttendanceShift }, employeeId: string, date: string) => Promise<void>
  onDeleteShift?: (shiftKey: string, employeeId: string, date: string) => Promise<void>
  companyKey?: string // For Firebase operations
}

export const ShiftConflictProvider: React.FC<ShiftConflictProviderProps> = ({ 
  children, 
  onResolveConflictCallback,
  onSaveShift,
  onDeleteShift,
  companyKey
}) => {
  const [state, dispatch] = useReducer(conflictReducer, initialState)

  const setConflicts = useCallback((conflicts: ConflictData[]) => {
    dispatch({ type: 'SET_CONFLICTS', payload: conflicts })
  }, [])

  const resolveConflict = useCallback(async (conflictId: string, resolution: ConflictResolution) => {
    try {
      dispatch({ type: 'SET_RESOLUTION_IN_PROGRESS', payload: { conflictId, inProgress: true } })
      
      // Parse conflict ID to extract components
      const [date, employeeId, shiftKey] = conflictId.split('-')
      
      // Handle Firebase persistence based on resolution type
      if (resolution.shouldPersist !== false) {
        if (resolution.type === 'approve' && resolution.modifiedShift) {
          // For approve: mark shift as confirmed and save
          const confirmedShift = { ...resolution.modifiedShift, isConfirmed: true }
          
          if (onSaveShift) {
            await onSaveShift({ [shiftKey]: confirmedShift }, employeeId, date)
          }
          
        } else if (resolution.type === 'modify' && resolution.modifiedShift) {
          // For modify: save the modified shift
          if (onSaveShift) {
            await onSaveShift({ [shiftKey]: resolution.modifiedShift }, employeeId, date)
          }
          
        } else if (resolution.type === 'delete') {
          // For delete: remove the shift
          if (onDeleteShift) {
            await onDeleteShift(shiftKey, employeeId, date)
          }
        }
      }
      
      // Call the custom callback if provided (for additional logic like activity logging)
      if (onResolveConflictCallback) {
        await onResolveConflictCallback(conflictId, {
          ...resolution,
          employeeId,
          date,
          shiftKey
        })
      }
      
      dispatch({ type: 'RESOLVE_CONFLICT', payload: { conflictId, resolution } })
    } catch (error) {
      console.error('Failed to resolve conflict:', error)
      dispatch({ type: 'SET_RESOLUTION_IN_PROGRESS', payload: { conflictId, inProgress: false } })
      throw error
    }
  }, [onResolveConflictCallback, onSaveShift, onDeleteShift])

  const undoResolve = useCallback((conflictId: string) => {
    dispatch({ type: 'UNDO_RESOLVE', payload: conflictId })
  }, [])

  const selectConflicts = useCallback((conflictIds: string[]) => {
    dispatch({ type: 'SELECT_CONFLICTS', payload: conflictIds })
  }, [])

  const setLoading = useCallback((loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading })
  }, [])

  const refreshConflicts = useCallback(() => {
    dispatch({ type: 'REFRESH_CONFLICTS' })
  }, [])

  const isConflictResolved = useCallback((conflictId: string): boolean => {
    return state.resolvedConflicts.includes(conflictId)
  }, [state.resolvedConflicts])

  const isResolutionInProgress = useCallback((conflictId: string): boolean => {
    return state.resolutionInProgress.has(conflictId)
  }, [state.resolutionInProgress])

  const getUnresolvedConflicts = useCallback((): ConflictData[] => {
    return state.conflicts.filter(conflict => !state.resolvedConflicts.includes(conflict.id))
  }, [state.conflicts, state.resolvedConflicts])

  const getConflictsByType = useCallback((): Record<string, ConflictData[]> => {
    const unresolvedConflicts = state.conflicts.filter(conflict => !state.resolvedConflicts.includes(conflict.id))
    const grouped: Record<string, ConflictData[]> = {}

    unresolvedConflicts.forEach(conflict => {
      const type = conflict.type || 'unknown'
      if (!grouped[type]) {
        grouped[type] = []
      }
      grouped[type].push(conflict)
    })

    return grouped
  }, [state.conflicts, state.resolvedConflicts])

  const actions = useMemo(() => ({
    setConflicts,
    resolveConflict,
    undoResolve,
    selectConflicts,
    setLoading,
    refreshConflicts,
    isConflictResolved,
    isResolutionInProgress,
    getUnresolvedConflicts,
    getConflictsByType
  }), [
    setConflicts,
    resolveConflict,
    undoResolve,
    selectConflicts,
    setLoading,
    refreshConflicts,
    isConflictResolved,
    isResolutionInProgress,
    getUnresolvedConflicts,
    getConflictsByType
  ])

  return (
    <ShiftConflictContext.Provider value={{ state, dispatch, actions }}>
      {children}
    </ShiftConflictContext.Provider>
  )
}

export const useShiftConflict = (): ShiftConflictContextType => {
  const context = useContext(ShiftConflictContext)
  if (!context) {
    throw new Error('useShiftConflict must be used within ShiftConflictProvider')
  }
  return context
}
