import React, { createContext, useContext, useReducer, useEffect, useMemo } from 'react'
import { analyzeShiftStatus, type ShiftAnalysis } from 'utils/payroll/shiftStatusAnalysis'
import { AttendanceShift } from 'types/attendance'
import dayjs from 'dayjs'

// Types
interface ShiftConflictState {
  analyses: { [shiftKey: string]: ShiftAnalysis }
  isAnalyzing: boolean
  lastAnalyzedAt: number
}

interface ShiftConflictContextValue {
  state: ShiftConflictState
  getShiftAnalysis: (shiftKey: string) => ShiftAnalysis | null
  analyzeShift: (shift: AttendanceShift, employeeId: string, date: string) => void
  analyzeAllShifts: (shifts: AttendanceShift[], employeeId: string, date: string) => void
  clearAnalyses: () => void
}

// Actions
type ShiftConflictAction =
  | { type: 'START_ANALYSIS' }
  | { type: 'SET_ANALYSIS'; payload: { shiftKey: string; analysis: ShiftAnalysis } }
  | { type: 'SET_MULTIPLE_ANALYSES'; payload: { [shiftKey: string]: ShiftAnalysis } }
  | { type: 'CLEAR_ANALYSES' }
  | { type: 'FINISH_ANALYSIS' }

// Reducer
const shiftConflictReducer = (state: ShiftConflictState, action: ShiftConflictAction): ShiftConflictState => {
  switch (action.type) {
    case 'START_ANALYSIS':
      return { ...state, isAnalyzing: true }
    
    case 'SET_ANALYSIS':
      return {
        ...state,
        analyses: {
          ...state.analyses,
          [action.payload.shiftKey]: action.payload.analysis
        }
      }
    
    case 'SET_MULTIPLE_ANALYSES':
      return {
        ...state,
        analyses: { ...state.analyses, ...action.payload }
      }
    
    case 'CLEAR_ANALYSES':
      return {
        ...state,
        analyses: {},
        lastAnalyzedAt: Date.now()
      }
    
    case 'FINISH_ANALYSIS':
      return {
        ...state,
        isAnalyzing: false,
        lastAnalyzedAt: Date.now()
      }
    
    default:
      return state
  }
}

// Context
const ShiftConflictContext = createContext<ShiftConflictContextValue | null>(null)

// Provider Props
interface ShiftConflictProviderProps {
  children: React.ReactNode
  currentCompany: any
  attendanceData: any
}

// Provider Component
export const ShiftConflictProvider: React.FC<ShiftConflictProviderProps> = ({
  children,
  currentCompany,
  attendanceData
}) => {
  const [state, dispatch] = useReducer(shiftConflictReducer, {
    analyses: {},
    isAnalyzing: false,
    lastAnalyzedAt: 0
  })

  // Stable reference to current company and attendance data
  const contextData = useMemo(() => ({
    currentCompany,
    attendanceData
  }), [currentCompany, attendanceData])

  const getShiftAnalysis = (shiftKey: string): ShiftAnalysis | null => {
    return state.analyses[shiftKey] || null
  }

  const analyzeShift = (shift: AttendanceShift, employeeId: string, date: string) => {
    if (!contextData.currentCompany || !contextData.attendanceData || !shift) return

    const shiftKey = (shift as any)?.shiftKey
    if (!shiftKey) return

    // Skip analysis for new shifts without start/end times
    const isNewShift = !shiftKey || (shift.manuallyCreated && !shift.start && !shift.end)
    if (isNewShift) return

    try {
      const analysis = analyzeShiftStatus(
        shift,
        employeeId,
        date,
        contextData.attendanceData,
        contextData.currentCompany,
        dayjs(date).isSame(dayjs(), 'day')
      )

      // Only update if analysis has changed
      const existingAnalysis = state.analyses[shiftKey]
      const hasChanged = !existingAnalysis ||
        existingAnalysis.status !== analysis.status ||
        JSON.stringify(existingAnalysis.issues) !== JSON.stringify(analysis.issues)

      if (hasChanged) {
        dispatch({
          type: 'SET_ANALYSIS',
          payload: { shiftKey, analysis }
        })
      }
    } catch (error) {
      console.error('Error analyzing shift:', error)
    }
  }

  const analyzeAllShifts = (shifts: AttendanceShift[], employeeId: string, date: string) => {
    if (!contextData.currentCompany || !contextData.attendanceData || !shifts.length) return

    dispatch({ type: 'START_ANALYSIS' })

    const newAnalyses: { [shiftKey: string]: ShiftAnalysis } = {}

    shifts.forEach(shift => {
      const shiftKey = (shift as any)?.shiftKey
      if (!shiftKey) return

      // Skip analysis for new shifts
      const isNewShift = shift.manuallyCreated && !shift.start && !shift.end
      if (isNewShift) return

      try {
        const analysis = analyzeShiftStatus(
          shift,
          employeeId,
          date,
          contextData.attendanceData,
          contextData.currentCompany,
          dayjs(date).isSame(dayjs(), 'day')
        )

        newAnalyses[shiftKey] = analysis
      } catch (error) {
        console.error('Error analyzing shift:', shiftKey, error)
      }
    })

    dispatch({
      type: 'SET_MULTIPLE_ANALYSES',
      payload: newAnalyses
    })

    dispatch({ type: 'FINISH_ANALYSIS' })
  }

  const clearAnalyses = () => {
    dispatch({ type: 'CLEAR_ANALYSES' })
  }

  // Auto-analyze when attendance data changes (debounced)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // This will be triggered by parent components when needed
      // Rather than automatically on every change
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [contextData.attendanceData, contextData.currentCompany])

  const contextValue: ShiftConflictContextValue = {
    state,
    getShiftAnalysis,
    analyzeShift,
    analyzeAllShifts,
    clearAnalyses
  }

  return (
    <ShiftConflictContext.Provider value={contextValue}>
      {children}
    </ShiftConflictContext.Provider>
  )
}

// Hook
export const useShiftConflict = () => {
  const context = useContext(ShiftConflictContext)
  if (!context) {
    throw new Error('useShiftConflict must be used within a ShiftConflictProvider')
  }
  return context
}

export default ShiftConflictContext
