import React, { forwardRef, useEffect, useMemo, useRef, useState } from 'react'
import Overlay from 'react-bootstrap/Overlay'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'
import { toast } from 'react-toastify'

import dayjs from 'dayjs'
import moment, { Moment } from 'moment'
import { RootState } from 'store/reducers'

import NumberFormatted from 'components/ui/NumberFormatted'

import ActivitiesPopover from './ActivitesPopover'
import DeleteShiftModal from './DeleteShiftModal'
import PayrollToaster from './PayrollToaster'

import {
  PopoverStyled,
  ContainerStyled,
  HeaderStyled,
  HeaderTitleStyled,
  ActivityButtonStyled,
  SearchIconStyled,
  CloseButtonStyled,
  InfoBlockStyled,
  AvatarInitialsStyled,
  InfoBlockTextStyled,
  ShiftListStyled,
  ShiftListItemStyled,
  // ConflictIndicatorStyled, // Unused
  PlusIconStyled,
  RoleBlockStyled,
  RoleBlockLabelStyled,
  RoleBlockSalaryStyled,
  CustomSelectStyled,
  ShiftBlockStyled,
  RowCellStyled,
  RowStyled,
  CustomTimePickerStyled,
  DeleteButtonStyled,
  DeleteIconStyled,
  BreakBlockStyled,
  DividerStyled,
  CustomCheckboxStyled,
  AddBreakButtonStyled,
  CloseButtonTooltipStyled,
  CloseIconStyled,
  ScrollBlockStyled,
  ButtonBlockStyled,
  ButtonWrapStyled,
  SaveButtonStyled,
  OrangeButtonStyled,
  GreyButtonStyled,
  TooltipDeleteButtonStyled,
  TotalBlockStyled,
  TotalBlockLabelStyled,
  TotalBlockValueStyled,
  TooltipStyled,
  WarningButtonStyled,
  WarningIconStyled,
  // DisabledButtonStyled // Unused
} from '../../styles/ShiftPopover.styles'

import { AttendanceShift, AttendanceShifts } from 'types/attendance'
import { Company } from 'types/company'
import { IEmployee } from 'types/employee'
import { Shift } from 'types/schedule'

import { getEmployeeRate } from 'utils/employees'
import { checkShiftOverlap } from 'utils/attendance'

import {
  analyzeShiftStatus,
  getShiftStatusColor,
  type ShiftAnalysis
} from 'utils/payroll/shiftStatusAnalysis'

import closeIcon from 'img/icons/closeIcon.svg'

// Helper function to convert ShiftAnalysis to button states (replacing getPopoverButtonStates)
const getButtonStatesFromShiftAnalysis = (analysis: ShiftAnalysis) => {
  const hasRedConflicts = analysis.status === 'overlapping'
  const hasGreyConflicts = analysis.status === 'other-role'
  const hasOrangeIssues = analysis.status === 'problematic'

  return {
    canSave: !hasRedConflicts && !hasGreyConflicts,
    canApprove: !hasRedConflicts && !hasGreyConflicts,
    showReclaim: hasOrangeIssues,
    saveButtonColor: analysis.status === 'approved' ? 'green' : 'orange',
    approveButtonColor: analysis.status === 'approved' ? 'green' : 'orange'
  }
}

// Helper function to convert color string to expected styled component type
const mapColorToConflictType = (colorString: string | undefined): 'green' | 'orange' | 'grey' | 'red' | undefined => {
  switch (colorString) {
    case 'blue':
      return undefined // Blue is handled differently in ShiftPopover (no conflict type for current shifts)
    case 'white':
      return 'green' // Map white (approved) to green
    case 'grey':
      return 'grey'
    case 'orange':
      return 'orange'
    case 'red':
      return 'red'
    default:
      return undefined
  }
}

// Extended shift type for enhanced properties
type ExtendedAttendanceShift = AttendanceShift & {
  isStartOverlapping?: boolean
  isEndOverlapping?: boolean
  scheduledShift?: Shift
}

// TODO SEVA - once ref is properly send => check TimePicker overflow
type ShiftPopoverProps = {
  onClose: () => void
  showActivityPopoverOnLeft?: boolean
  employee?: IEmployee
  date?: string
  shifts?: AttendanceShift[]
  isNewShift?: boolean
  currentCompany?: Company
  attendanceData?: AttendanceShifts
  onSave?: (newShift: { [key: string]: AttendanceShift }) => void
  onDeleteShift?: (shiftKey: string) => void
}

const ShiftPopover = forwardRef<HTMLDivElement, ShiftPopoverProps>(
  (
    {
      onClose,
      showActivityPopoverOnLeft,
      employee,
      date,
      shifts = [],
      isNewShift = false,
      currentCompany,
      attendanceData,
      onSave,
      onDeleteShift,
      ...rest
    },
    ref
  ) => {
    const [activeShift, setActiveShift] = useState<number | null>(0)

    // Track unsaved changes per shift
    const [unsavedChanges, setUnsavedChanges] = useState<{[shiftIndex: number]: Partial<AttendanceShift>}>({})

    // Track when we're waiting for a new shift to appear after creation
    const [waitingForNewShift, setWaitingForNewShift] = useState<number | null>(null)

    const isLocaleFr =
      useSelector((state: RootState) => state.i18n.locale) === 'fr'

    // Generate position options matching the exact logic from EmployeeRolesList.js
    const options = React.useMemo(() => {
      if (!currentCompany?.jobs || !employee?.positions) {
        return []
      }

      const positionOptions: Array<{ label: string; value: string }> = []

      // Clone and sort jobs by priority (matching EmployeeRolesList.js logic)
      const jobsCopy = { ...currentCompany.jobs }
      const sortedJobs = Object.entries(jobsCopy)
        .filter(([_, value]) => value && !value.archived)
        .map(([key, value]) => ({ ...value, key }))
        .sort((a, b) => a.priority - b.priority)

      // Process each job category
      sortedJobs.forEach((category) => {
        // Check if this employee has this category assigned
        const hasCategory = employee.positions?.some(p => p.categoryId === category.key) || false

        if (hasCategory) {
          // Only show the main category name, not subcategories
          positionOptions.push({
            label: category.name,
            value: category.key
          })
        }
      })

      return positionOptions
    }, [currentCompany?.jobs, employee?.positions])

    const [selectedRole, setSelectedRole] = useState<string | null>(null)

    const [roundedTimeStart, setRoundedTimeStart] = useState<Moment | null>(
      null
    )
    const [roundedTimeEnd, setRoundedTimeEnd] = useState<Moment | null>(null)

    // Conflict analysis state - only use hoursTableAnalysis for consistency
    const [hoursTableAnalysis, setHoursTableAnalysis] = useState<ShiftAnalysis | null>(null)    // Separate state for each tooltip
    const [showPlannedTooltip, setShowPlannedTooltip] = useState(true)
    const [showClockedTooltip, setShowClockedTooltip] = useState(false)
    // Removed showShortShiftTooltip - shift length validation disabled
    const [showOverlapTooltip, setShowOverlapTooltip] = useState(false)
    const [showRoleTooltip, setShowRoleTooltip] = useState(false)
    const [canShowTooltip, setCanShowTooltip] = useState(false)
    const [showDeleteBreakTooltip, setShowDeleteBreakTooltip] = useState<string | null>(null) // Track which break ID's tooltip is shown

    // Separate refs for each tooltip
    const plannedTooltipRef = useRef<HTMLButtonElement>(null)
    const clockedTooltipRef = useRef<HTMLButtonElement>(null)
   // const shortShiftTooltipRef = useRef<HTMLButtonElement>(null)
    const overlapTooltipRef = useRef<HTMLButtonElement>(null)
    const roleDropdownRef = useRef<HTMLDivElement>(null)
    const prevActiveShiftRef = useRef<number | null>(null)
    const deleteBreakRefs = useRef<{ [breakId: string]: HTMLButtonElement | null }>({}) // Store refs for each delete break button
    const prevAnalysisRef = useRef<ShiftAnalysis | null>(null) // Reference to track previous analysis to prevent infinite loops

    //
    const [showActivityPopover, setShowActivityPopover] = useState(false)

    // Clear unsaved changes when shifts prop changes from parent
    useEffect(() => {
      setUnsavedChanges({}) // Clear unsaved changes when props update
    }, [shifts])

    // Watch for new shifts appearing after creation
    useEffect(() => {
      if (waitingForNewShift !== null && shifts.length > waitingForNewShift) {
        // New shift has appeared, switch to it
        setActiveShift(waitingForNewShift)
        setWaitingForNewShift(null)
      }
    }, [shifts.length, waitingForNewShift])

    // Helper function to update local shift data without saving to Firebase
    const updateLocalShift = React.useCallback((shiftIndex: number, updates: Partial<AttendanceShift>) => {
      setUnsavedChanges(prev => {
        const currentUnsaved = prev[shiftIndex] || {}
        let hasChanges = false;
        for (const [key, newValue] of Object.entries(updates)) {
          if (key === 'breaks') {
            const currentBreaks = currentUnsaved[key] || {};
            const newBreaks = newValue || {};
            if (JSON.stringify(currentBreaks) !== JSON.stringify(newBreaks)) {
              hasChanges = true;
              break;
            }
          } else if (currentUnsaved[key as keyof AttendanceShift] !== newValue) {
            hasChanges = true;
            break;
          }
        }
        if (hasChanges) {
          return {
            ...prev,
            [shiftIndex]: { ...currentUnsaved, ...updates }
          };
        }
        return prev;
      });
    }, []); // Safe: only uses functional update

    // Helper function to save all local changes to Firebase
    const saveLocalChanges = async () => {
      if (!onSave || !employee || !date) return

      try {
        const shiftsToSave: { [key: string]: AttendanceShift } = {}
        
        Object.entries(unsavedChanges).forEach(([shiftIndexStr, changes]) => {
          const shiftIndex = parseInt(shiftIndexStr)
          const shift = shifts[shiftIndex]
          
          if (shift) {
            // Existing shift - update it
            const shiftKey = (shift as any)?.shiftKey || `shift_${Date.now()}_${shiftIndex}`
            shiftsToSave[shiftKey] = { ...shift, ...changes }
          } else if (shiftIndex >= shifts.length) {
            // New shift - create it
            const newShiftKey = `shift_${Date.now()}_${shiftIndex}`
            shiftsToSave[newShiftKey] = {
              start: undefined,
              end: undefined,
              breaks: {},
              positionId: '',
              isConfirmed: false,
              manuallyCreated: true,
              ...changes // Apply the unsaved changes
            } as AttendanceShift
          }
        })

        if (Object.keys(shiftsToSave).length > 0) {
          await onSave(shiftsToSave)
          setUnsavedChanges({}) // Clear unsaved changes after successful save
        }
      } catch (error) {
        console.error('Failed to save local changes:', error)
        throw error
      }
    }

    // Get current shift data or create new shift template
    const currentShift: ExtendedAttendanceShift | null = useMemo(() => {
      if (activeShift === null) return null

      // If activeShift index exists in shifts array (the original prop), work with that
      if (shifts && activeShift < shifts.length) {
        // Use the original shift from props, then apply local changes
        const baseShift = shifts[activeShift] // Use original shifts, not localShifts
        const changes = unsavedChanges[activeShift] || {}

        // Deep merge breaks if both exist - properly handle deletions
        const mergedShift = { ...baseShift, ...changes }
        if (changes.breaks) {
          // If changes.breaks exists, use it as the complete breaks object
          // This ensures deletions are properly reflected
          mergedShift.breaks = { ...changes.breaks }
        } else if (baseShift.breaks) {
          // If no changes to breaks, use the base breaks
          mergedShift.breaks = { ...baseShift.breaks }
        }
        
        return mergedShift as ExtendedAttendanceShift
      }

      // If activeShift equals shifts.length, we're creating a new shift
      if (shifts && activeShift === shifts.length) {
        const changes = unsavedChanges[activeShift] || {}
        const newShift = {
          start: undefined,
          end: undefined,
          breaks: {},
          positionId: selectedRole || Object.keys(currentCompany?.jobs || {})[0] || '',
          isConfirmed: false,
          manuallyCreated: true,
          ...changes // Apply any unsaved changes
        } as ExtendedAttendanceShift
        
        // Handle breaks separately to ensure proper merging
        if (changes.breaks) {
          newShift.breaks = { ...changes.breaks }
        }
        
        console.log('🆕 Returning new shift:', newShift)
        return newShift
      }

      console.log('❌ No shift found, returning null')
      return null
    }, [activeShift, shifts, unsavedChanges, selectedRole, currentCompany?.jobs]) // Use shifts instead of localShifts

    // Determine shift status based on real data
    const hasWarning = isNewShift
      ? false
      : currentShift
        ? !currentShift.positionId
        : false
    // Check if employee clocked in but didn't clock out using attendance data
    const notClockedOut = React.useMemo(() => {
      if (!attendanceData || !date || !employee || !currentShift) return false

      const dayAttendance = attendanceData[date]
      if (!dayAttendance) return false

      const employeeAttendance = dayAttendance[employee.uid]
      if (!employeeAttendance) return false

      const currentShiftKey = (currentShift as any)?.shiftKey
      if (!currentShiftKey) return false

      const attendanceShift = employeeAttendance[currentShiftKey]
      if (!attendanceShift) return false

      // Employee clocked in but didn't clock out
      return attendanceShift.start && !attendanceShift.end
    }, [attendanceData, date, employee, currentShift])

    // Remove shift duration validation - managers can book any length shifts
    //const isShortShift = false // Disabled as per requirement

    // Use proper checkShiftOverlap function directly (same as PayrollOld)
    const isOverlap = React.useMemo(() => {
      // Don't check overlaps for new shifts (they don't exist in attendance data yet)
      if (isNewShift || !currentShift || !attendanceData || !date || !employee) return false

      // Get all shifts for this employee on this date
      const dayAttendance = attendanceData[date]
      if (!dayAttendance) return false

      const employeeAttendance = dayAttendance[employee.uid]
      if (!employeeAttendance) return false

      // Convert attendance data to format expected by checkShiftOverlap
      const employeeShifts: { [key: string]: AttendanceShift } = {}
      Object.entries(employeeAttendance).forEach(([shiftKey, shift]) => {
        if (shift && typeof shift === 'object' && 'start' in shift) {
          employeeShifts[shiftKey] = shift as AttendanceShift
        }
      })

      // Only check for overlaps if there are multiple shifts
      if (Object.keys(employeeShifts).length < 2) return false

      // Get previous and next day shifts for cross-day overlap detection
      const previousDate = dayjs(date).subtract(1, 'day').format('YYYY-MM-DD')
      const nextDate = dayjs(date).add(1, 'day').format('YYYY-MM-DD')

      const previousDayShifts: { [key: string]: AttendanceShift } = {}
      const nextDayShifts: { [key: string]: AttendanceShift } = {}

      // Get previous day shifts
      const prevDayAttendance = attendanceData[previousDate]?.[employee.uid]
      if (prevDayAttendance) {
        Object.entries(prevDayAttendance).forEach(([shiftKey, shift]) => {
          if (shift && typeof shift === 'object' && 'start' in shift) {
            previousDayShifts[shiftKey] = shift as AttendanceShift
          }
        })
      }

      // Get next day shifts
      const nextDayAttendance = attendanceData[nextDate]?.[employee.uid]
      if (nextDayAttendance) {
        Object.entries(nextDayAttendance).forEach(([shiftKey, shift]) => {
          if (shift && typeof shift === 'object' && 'start' in shift) {
            nextDayShifts[shiftKey] = shift as AttendanceShift
          }
        })
      }



      // Use the same overlap detection as PayrollOld
      const overlappedShifts = checkShiftOverlap({
        employeeShifts,
        previousDayShifts,
        nextDayShifts
      })

      // Check if current shift has overlaps
      const currentShiftKey = (currentShift as any)?.shiftKey
      if (currentShiftKey && overlappedShifts[currentShiftKey]) {
        const shiftOverlap = overlappedShifts[currentShiftKey]
        return shiftOverlap.isStartOverlapping || shiftOverlap.isEndOverlapping
      }

      return false
    }, [isNewShift, currentShift, attendanceData, date, employee])

    // Calculate total hours - memoized to update when shift times or breaks change
    const totalHours = React.useMemo(() => {

      
      // Calculate actual hours based on start and end time
      // Use currentShift from the logic above (handles both existing and new shifts)
      if (!currentShift || !currentShift.start) return '0'

      // Convert timestamps to minutes since midnight
      const startMinutes = currentShift.start
      let endMinutes = currentShift.end

      // If no end time (not clocked out), use default 6.5 hours
      if (!endMinutes) {
        endMinutes = startMinutes + (6.5 * 60) // 6.5 hours in minutes
      }

      // Calculate total minutes worked
      let totalMinutes = endMinutes - startMinutes

      // Handle overnight shifts (end time is next day)
      if (totalMinutes < 0) {
        totalMinutes += 24 * 60 // Add 24 hours in minutes
      }

      // Subtract only unpaid break time
      const breakMinutes = Object.values(currentShift.breaks || {}).reduce((total, breakItem) => {
        // Only subtract unpaid breaks from total hours
        const isUnpaid = (breakItem as any).isUnpaid !== false // Default to true if not explicitly set to false
        return total + (isUnpaid ? (breakItem.lengthRounded || 0) : 0)
      }, 0)

      const workMinutes = totalMinutes - breakMinutes
      const workHours = workMinutes / 60

      const result = Math.max(0, workHours).toFixed(1)

      
      return result
    }, [currentShift])

    // Calculate total hours for all shifts - memoized to update when any shift changes
    const totalAllShiftsHours = React.useMemo(() => {
      // Calculate total hours including current shift being edited
      let totalHours = 0

      // Add hours from existing shifts (use original shifts array)
      shifts.forEach((shift, index) => {
        // Skip the current shift if we're editing it
        if (index === activeShift) return

        // Apply any local changes to this shift
        const shiftWithChanges = unsavedChanges[index] ? { ...shift, ...unsavedChanges[index] } : shift

        if (!shiftWithChanges.start || !shiftWithChanges.end) return

        let shiftMinutes = shiftWithChanges.end - shiftWithChanges.start
        if (shiftMinutes < 0) shiftMinutes += 24 * 60
        const breakMinutes = Object.values(shiftWithChanges.breaks || {}).reduce((breakTotal, breakItem) => {
          // Only subtract unpaid breaks from total hours
          const isUnpaid = (breakItem as any).isUnpaid !== false // Default to true if not explicitly set to false
          return breakTotal + (isUnpaid ? (breakItem.lengthRounded || 0) : 0)
        }, 0)

        const workMinutes = shiftMinutes - breakMinutes
        totalHours += workMinutes / 60
      })

      // Add hours from current shift being edited
      if (currentShift && currentShift.start) {
        const startMinutes = currentShift.start
        let endMinutes = currentShift.end || (startMinutes + 6.5 * 60) // Default 6.5 hours

        let shiftMinutes = endMinutes - startMinutes
        if (shiftMinutes < 0) shiftMinutes += 24 * 60

        // Calculate break minutes from current shift data (includes local changes)
        const breakMinutes = Object.values(currentShift.breaks || {}).reduce((breakTotal, breakItem) => {
          // Only subtract unpaid breaks from total hours
          const isUnpaid = (breakItem as any).isUnpaid !== false // Default to true if not explicitly set to false
          return breakTotal + (isUnpaid ? (breakItem.lengthRounded || 0) : 0)
        }, 0)

        const workMinutes = shiftMinutes - breakMinutes
        totalHours += workMinutes / 60
      }

      return Math.max(0, totalHours).toFixed(2)
    }, [shifts, unsavedChanges, activeShift, currentShift]) // Use shifts instead of localShifts

    // Initialize selectedRole from current shift data
    useEffect(() => {
      if (currentShift?.positionId) {
        setSelectedRole(currentShift.positionId)
      }
    }, [currentShift?.positionId])

    // Auto-assign role if only one role is available
    useEffect(() => {
      if (options.length === 1 && !selectedRole) {
        const singleRole = options[0].value;
        setSelectedRole(singleRole);
        // Only update local state for new shifts (not existing)
        if (activeShift !== null && activeShift >= shifts.length) {
          updateLocalShift(activeShift, { positionId: singleRole });
        }
      }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [options, selectedRole, activeShift, shifts.length]); // updateLocalShift is stable with empty deps

    // Initialize time pickers from current shift data (including local changes)
    useEffect(() => {
      // Track if activeShift changed to avoid clearing during editing
      const activeShiftChanged = prevActiveShiftRef.current !== activeShift
      prevActiveShiftRef.current = activeShift

      // Sync time pickers with currentShift data
      if (currentShift?.start) {
        const startMoment = moment()
          .startOf('day')
          .add(currentShift.start, 'minutes')
        setRoundedTimeStart(startMoment)
      } else if (activeShiftChanged) {
        // Only clear if we're switching to a different shift, not during editing
        setRoundedTimeStart(null)
      }

      if (currentShift?.end) {
        const endMoment = moment()
          .startOf('day')
          .add(currentShift.end, 'minutes')
        setRoundedTimeEnd(endMoment)
      } else if (activeShiftChanged) {
        // Only clear if we're switching to a different shift, not during editing
        setRoundedTimeEnd(null)
      }
    }, [currentShift?.start, currentShift?.end, activeShift])



    // Analyze shift for conflicts when shift data changes
    useEffect(() => {
      if (currentShift && employee && date && attendanceData && currentCompany) {
        const originalShift = activeShift !== null && shifts[activeShift] ? shifts[activeShift] : currentShift;
        const isNewShiftForAnalysis = !originalShift || !(originalShift as any).shiftKey || (originalShift.manuallyCreated && !originalShift.start && !originalShift.end);
        if (isNewShiftForAnalysis) {
          if (hoursTableAnalysis !== null) {
            setHoursTableAnalysis(null);
          }
          return;
        }
        const analysis = analyzeShiftStatus(
          originalShift,
          employee.uid,
          date,
          attendanceData,
          currentCompany,
          dayjs(date).isSame(dayjs(), 'day')
        );
        
        // Compare with previous analysis to avoid unnecessary state updates
        const prevAnalysis = prevAnalysisRef.current;
        const analysisChanged = !prevAnalysis || 
          prevAnalysis.status !== analysis.status || 
          JSON.stringify(prevAnalysis.issues) !== JSON.stringify(analysis.issues);
        
        // Only update state if analysis actually changed
        if (analysisChanged) {
          setHoursTableAnalysis(analysis);
          prevAnalysisRef.current = analysis;
        }
        
        if (analysis.issues.length > 0) {
          const timer = setTimeout(() => {
            setCanShowTooltip(true);
            setShowPlannedTooltip(true);
          }, 100);
          return () => clearTimeout(timer);
        }
      }
    }, [currentShift, activeShift, shifts, employee, date, attendanceData, currentCompany]);

    // Delay showing tooltip to allow parent popover to position itself
    useEffect(() => {
      if (hasWarning) {
        const timer = setTimeout(() => {
          setCanShowTooltip(true)
        }, 100) // Small delay to ensure parent popover is positioned

        return () => clearTimeout(timer)
      }
    }, [hasWarning])

    const handleClosePopover = () => {
      onClose()
      setShowPlannedTooltip(false)
      setShowClockedTooltip(false)
      // Removed setShowShortShiftTooltip - shift length validation disabled
      setShowOverlapTooltip(false)
      setShowDeleteBreakTooltip(null) // Close delete break tooltip when popover is closed
    }

    // Handle break deletion with confirmation
    const handleDeleteBreak = (breakId: string) => {
      console.log('🗑️ Delete break confirmed:', {
        breakId,
        activeShift,
        currentShift: currentShift ? 'exists' : 'null'
      });
      
      if (activeShift !== null && currentShift) {
        const allBreaks = currentShift.breaks || {}
        const breakCount = Object.keys(allBreaks).length
        
        if (breakCount > 1) {
          // Delete this specific break if more than one exists
          const updatedBreaks = { ...allBreaks }
          delete updatedBreaks[breakId]
          updateLocalShift(activeShift, { breaks: updatedBreaks })
        } else {
          // If it's the only break, clear its data but keep the break row
          const updatedBreaks = {
            ...allBreaks,
            [breakId]: {
              start: 0,
              end: 0,
              lengthRounded: 0,
              isUnpaid: true // Reset to default unpaid
            }
          }
          updateLocalShift(activeShift, { breaks: updatedBreaks })
        }
      }
      
      // Close the tooltip
      setShowDeleteBreakTooltip(null)
    }

    const activityPopoverRef = useRef<HTMLDivElement>(null)
    const [showDeleteShiftModal, setShowDeleteShiftModal] = useState(false)

    const canReclaim = !isNewShift && currentShift && currentShift.positionId
    const saveAmount = 5.4
    const [hasClaimed, setHasClaimed] = useState(false)

    const onChangeStatus = async (
      status: 'approve' | 'delete' | 'claim' | 'modify',
      onUndo?: () => void
    ) => {

      if (!currentShift || !onSave || !employee || !date) {
        toast.error('Missing required data to update shift status')
        return
      }

      // Validate role selection for approve action
      if (status === 'approve' && !selectedRole) {
        // Show tooltip on role dropdown
        setShowRoleTooltip(true)
        setCanShowTooltip(true)

        // Auto-hide tooltip after 3 seconds
        setTimeout(() => {
          setShowRoleTooltip(false)
        }, 3000)

        toast.error('Please select a role before approving the shift')
        return
      }

      try {
        if (status === 'approve') {
          // Mark shift as confirmed/approved
          const approvedShift = {
            ...currentShift,
            isConfirmed: true,
            positionId: selectedRole || currentShift.positionId,
            // Use current shift breaks (they are already updated via local state)
            breaks: currentShift.breaks
          }

          // Determine if this is a new shift or existing shift
          const isActuallyNewShift = activeShift !== null && activeShift >= shifts.length
          
          let shiftKey: string
          if (isActuallyNewShift) {
            // Creating a new shift
            shiftKey = `shift_${Date.now()}`
            console.log('🆕 Creating new shift with key:', shiftKey)
          } else {
            // Updating existing shift - get the shiftKey from the shifts array
            if (activeShift === null || activeShift >= shifts.length) {
              console.error('Invalid activeShift index for existing shift:', { activeShift, shiftsLength: shifts.length })
              toast.error('Unable to save shift: invalid shift index')
              return
            }

            const shiftWithKey = shifts[activeShift]
            shiftKey = (shiftWithKey as any)?.shiftKey

            if (!shiftKey) {
              console.error('No shiftKey found for existing shift:', { activeShift, shift: shiftWithKey })
              toast.error('Unable to save shift: missing shift key')
              return
            }
            
            console.log('📝 Updating existing shift with key:', shiftKey)
          }

          console.log('💾 Saving shift:', { shiftKey, approvedShift })
          await onSave({ [shiftKey]: approvedShift })
        } else if (status === 'delete') {
          // Handle shift deletion
          if (!onDeleteShift) {
            toast.error('Unable to delete shift: delete function not available')
            return
          }

          const shiftKey = (currentShift as any)?.shiftKey
          if (!shiftKey) {
            toast.error('Unable to delete shift: no shift key found')
            return
          }

          console.log('🗑️ Deleting shift with key:', shiftKey)
          await onDeleteShift(shiftKey)
          
          // Show specific success message for deletion
          toast.success('Shift deleted successfully!')
          return // Don't show the generic PayrollToaster for delete actions
        } else if (status === 'claim') {
          // Handle reclaim functionality - reset to scheduled times
          if (!currentShift.scheduledShift?.start || !currentShift.scheduledShift?.end) {
            toast.error('Unable to reclaim: no scheduled shift times available')
            return
          }

          // Create the reclaimed shift with scheduled times
          const reclaimedShift = {
            ...currentShift,
            start: currentShift.scheduledShift.start,
            end: currentShift.scheduledShift.end,
            isConfirmed: true,
            positionId: selectedRole || currentShift.positionId,
            // Keep existing breaks
            breaks: currentShift.breaks
          }

          // Get the shift key
          const shiftKey = (currentShift as any)?.shiftKey
          if (!shiftKey) {
            console.error('No shiftKey found for reclaim:', currentShift)
            toast.error('Unable to reclaim shift: missing shift key')
            return
          }

          console.log('🔄 Reclaiming shift with scheduled times:', { 
            shiftKey, 
            originalStart: currentShift.start, 
            originalEnd: currentShift.end,
            scheduledStart: currentShift.scheduledShift.start,
            scheduledEnd: currentShift.scheduledShift.end,
            reclaimedShift 
          })
          
          await onSave({ [shiftKey]: reclaimedShift })
          
          // Update local state to reflect the reclaim
          if (activeShift !== null) {
            updateLocalShift(activeShift, {
              start: currentShift.scheduledShift.start,
              end: currentShift.scheduledShift.end,
              isConfirmed: true
            })
            
            // Update the time pickers to reflect the new times
            const startMoment = moment().startOf('day').add(currentShift.scheduledShift.start, 'minutes')
            const endMoment = moment().startOf('day').add(currentShift.scheduledShift.end, 'minutes')
            setRoundedTimeStart(startMoment)
            setRoundedTimeEnd(endMoment)
          }
          
          toast.success('Shift reclaimed to scheduled times!')
          return // Don't show the generic PayrollToaster for reclaim actions
        }

        // Show success toast for non-delete/reclaim actions
        toast(
          <PayrollToaster
            onUndo={onUndo}
            status={status}
          />,
          {
            className: 'payroll-toaster',
            closeButton: false,
            hideProgressBar: true,
            position: 'top-right',
            autoClose: 5000,
            pauseOnFocusLoss: false
          }
        )
      } catch (error) {
        // Specific error messages based on status
        const errorMessage = status === 'delete' 
          ? 'Failed to delete shift' 
          : status === 'claim'
          ? 'Failed to reclaim shift'
          : 'Failed to update shift status'
        toast.error(errorMessage)
        console.error(`Error ${status} shift status:`, error)
      }
    }

    // Get employee rate based on selected role
    // Note: Employee rates are stored separately in EmployeeRates/{companyId}/{employeeId}/{positionId}
    // For now, we'll use the position's default rate from company jobs
    const { rate, type, additionalSalary } = getEmployeeRate({
      employeeRate: {}, // Would need to fetch from EmployeeRates in real implementation
      positionId: selectedRole || '',
      jobs: currentCompany?.jobs || {}
    })

    const numericRate = Number(rate) || 0
    const formattedSalary = isLocaleFr
      ? numericRate.toLocaleString('fr-FR', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
      : numericRate.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })

    // Display rate type and bonus information
    const hasBonus = additionalSalary && additionalSalary > 0
    const bonusAmount = Number(additionalSalary) || 0

    // Handle case when employee data is not available
    // TODO Lie - udate with proper styles, translations if this block is needed
    if (!employee || !date) {
      return (
        <PopoverStyled
          {...rest}
          id='payroll_shift-popover'
          ref={ref}
        >
          <ContainerStyled>
            <HeaderStyled>
              <HeaderTitleStyled>Shift Details</HeaderTitleStyled>
              <CloseButtonStyled onClick={handleClosePopover}>
                <img
                  src={closeIcon}
                  alt=''
                />
              </CloseButtonStyled>
            </HeaderStyled>
            <div style={{ padding: '1rem', textAlign: 'center' }}>
              <p>Unable to load shift data.</p>
              <p
                style={{
                  fontSize: '0.8rem',
                  color: '#666',
                  marginTop: '0.5rem'
                }}
              >
                Employee:{' '}
                {employee
                  ? `${employee.name} ${employee.surname}`
                  : 'Not found'}
              </p>
              <p style={{ fontSize: '0.8rem', color: '#666' }}>
                Date: {date || 'Not provided'}
              </p>
            </div>
          </ContainerStyled>
        </PopoverStyled>
      )
    }
    return (
      <>
        <PopoverStyled
          {...rest}
          id='payroll_shift-popover'
          ref={ref}
        >
          {showDeleteShiftModal && (
            <DeleteShiftModal
              onClose={() => setShowDeleteShiftModal(false)}
              onDelete={() => {
                onChangeStatus('delete')
                setShowDeleteShiftModal(false)
                onClose()
              }}
            />
          )}
          <ContainerStyled ref={activityPopoverRef}>
            <HeaderStyled>
              <HeaderTitleStyled>
                {dayjs(date).format('dddd D')}
                {/* Show indicator if there are unsaved changes */}
                {Object.keys(unsavedChanges).length > 0 && (
                  <span style={{ color: '#ff9500', marginLeft: '8px', fontSize: '0.8em' }}>
                    • Unsaved changes
                  </span>
                )}
              </HeaderTitleStyled>
              <ActivityButtonStyled
                onClick={() => setShowActivityPopover(!showActivityPopover)}
              >
                <SearchIconStyled />
                {I18n.t('payroll.activities')}
              </ActivityButtonStyled>
              <CloseButtonStyled onClick={handleClosePopover}>
                <img
                  src={closeIcon}
                  alt=''
                />
              </CloseButtonStyled>
            </HeaderStyled>
            <InfoBlockStyled>
              <AvatarInitialsStyled employee={employee} />
              <InfoBlockTextStyled>
                {employee.name} {employee.surname}
                <span style={{ fontSize: '0.8rem', color: '#666', marginLeft: '0.5rem' }}>
                  #{employee?.payrollId || employee?.customId || 'N/A'}
                </span>
              </InfoBlockTextStyled>
            </InfoBlockStyled>
            <ShiftListStyled>
              {/* Render existing shifts (Shift 1, Shift 2, etc.) */}
              {shifts.map((shift, index) => {
                // Get the shift with any local changes applied for display purposes
                const shiftWithChanges = unsavedChanges[index] ? { ...shift, ...unsavedChanges[index] } : shift

                // Check if this is a new shift (doesn't have a shiftKey or was manually created without being saved)
                const isNewShiftTab = !shift || !(shift as any).shiftKey || (shift.manuallyCreated && !shift.start && !shift.end)

                // Analyze each shift for conflicts to show indicators - skip analysis for new shifts
                const shiftAnalysisForTab = !isNewShiftTab && employee && date && attendanceData && currentCompany
                  ? analyzeShiftStatus(
                      shift, // Use original shift data, not shiftWithChanges
                      employee.uid,
                      date,
                      attendanceData,
                      currentCompany,
                      dayjs(date).isSame(dayjs(), 'day')
                    )
                  : null

                // Apply status color based on shift analysis - use the same function as HoursTable
                const statusColor = shiftAnalysisForTab ? getShiftStatusColor(shiftAnalysisForTab.status) : undefined
                let conflictTypeForDisplay = mapColorToConflictType(statusColor)

                // Format shift times for display
                const formatShiftTime = (minutes: number | undefined) => {
                  if (!minutes && minutes !== 0) return '--:--'
                  const hours = Math.floor(minutes / 60)
                  const mins = minutes % 60
                  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
                }

                const shiftTimeDisplay = shiftWithChanges.start || shiftWithChanges.end
                  ? `${formatShiftTime(shiftWithChanges.start)} - ${formatShiftTime(shiftWithChanges.end)}`
                  : 'No time set'

                return (
                  <ShiftListItemStyled
                    key={index}
                    $isActive={activeShift === index}
                    $conflictType={conflictTypeForDisplay}
                    onClick={() => setActiveShift(index)}
                    title={shiftTimeDisplay} // Show time on hover
                  >
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <span>{I18n.t('common.shift')} {index + 1}</span>
                    </div>
                  </ShiftListItemStyled>
                )
              })}

              {/* Show "New Shift +" tab when no shifts exist, or "+" tab when shifts exist but less than 4 */}
              {shifts.length < 4 && (
                <ShiftListItemStyled
                  $isActive={activeShift === shifts.length}
                  onClick={async () => {
                    if (!onSave || !employee || !date) {
                      toast.error('Unable to create new shift: missing required data')
                      return
                    }

                    try {
                      // Create a new shift with default values
                      const newShift = {
                        start: undefined,
                        end: undefined,
                        breaks: {},
                        positionId: selectedRole || Object.keys(currentCompany?.jobs || {})[0] || '',
                        isConfirmed: false,
                        manuallyCreated: true
                      }

                      // Generate a new shift key
                      const newShiftKey = `shift_${Date.now()}`

                      console.log('🆕 Creating new shift:', { newShiftKey, newShift })

                      // Save the new shift to Firebase
                      await onSave({ [newShiftKey]: newShift })

                      // Set up waiting for the new shift to appear in the shifts prop
                      // Once the parent component updates attendanceData, the new shift will
                      // appear and we'll automatically switch to it
                      setWaitingForNewShift(shifts.length)

                      toast.success('New shift created successfully!')

                    } catch (error) {
                      console.error('❌ Failed to create new shift:', error)
                      toast.error('Failed to create new shift')
                    }
                  }}
                >
                  {shifts.length === 0 ? 'New Shift +' : '+'}
                </ShiftListItemStyled>
              )}
            </ShiftListStyled>
            <RoleBlockStyled ref={roleDropdownRef}>
              <RoleBlockLabelStyled>
                {I18n.t('common.role')}
              </RoleBlockLabelStyled>
              <CustomSelectStyled
                options={options}
                value={options.find(option => option.value === selectedRole)}
                onChange={option => {
                  if (option) {
                    setSelectedRole(option.value as string)
                    // Update local state instead of immediately saving to Firebase
                    if (activeShift !== null) {
                      updateLocalShift(activeShift, { positionId: option.value as string })
                    }
                    // Hide role tooltip if it's showing
                    setShowRoleTooltip(false)
                  }
                }}
                placeholder={I18n.t('payroll.select_role')}
                noOptionsMessage={I18n.t('payroll.no_roles_available')}
                components={{
                  IndicatorSeparator: null
                }}
                $noValue={!selectedRole}
              />
              <RoleBlockSalaryStyled>
                {/* Display rate based on type */}
                {type === 'hourly' && (
                  <div>{formattedSalary}/hour</div>
                )}
                {type === 'yearly' && (
                  <div>
                    <div>{formattedSalary}/year</div>
                    <div style={{ fontSize: '0.75rem', color: '#666', marginTop: '2px' }}>
                      ({(numericRate / 365).toLocaleString(isLocaleFr ? 'fr-FR' : 'en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      })}/day)
                    </div>
                  </div>
                )}

                {/* Show additional salary (bonus per shift) if available */}
                {hasBonus && (
                  <div style={{ fontSize: '0.75rem', backgroundColor: '#a4cbb0', marginTop: '2px' }}>
                    + {bonusAmount.toLocaleString(isLocaleFr ? 'fr-FR' : 'en-US', {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    })}
                  </div>
                )}
              </RoleBlockSalaryStyled>
            </RoleBlockStyled>
            <DividerStyled />
            <ScrollBlockStyled>
              <ShiftBlockStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('common.shift')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.start')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('common.end')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.total')}</RowCellStyled>
                </RowStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.planned')}</RowCellStyled>
                  <RowCellStyled>
                    {currentShift?.scheduledShift?.start
                      ? `${Math.floor(currentShift.scheduledShift.start / 60)}:${(currentShift.scheduledShift.start % 60).toString().padStart(2, '0')}`
                      : '--:--'}
                  </RowCellStyled>
                  <RowCellStyled>
                    {currentShift?.scheduledShift?.end
                      ? `${Math.floor(currentShift.scheduledShift.end / 60)}:${(currentShift.scheduledShift.end % 60).toString().padStart(2, '0')}`
                      : '--:--'}
                  </RowCellStyled>
                  <RowCellStyled>
                    {(hasWarning || (hoursTableAnalysis && hoursTableAnalysis.issues.length > 0)) && (
                      <WarningTooltipButton
                        isRed={hoursTableAnalysis?.status === 'overlapping'}
                        tooltipText={
                          hoursTableAnalysis?.issues.join(', ') ||
                          I18n.t('payroll.shift_not_planned_for_employee')
                        }
                        show={showPlannedTooltip}
                        canShow={canShowTooltip}
                        onToggleTooltip={() =>
                          setShowPlannedTooltip(!showPlannedTooltip)
                        }
                        onHide={() => setShowPlannedTooltip(false)}
                        buttonRef={plannedTooltipRef}
                      />
                    )}
                  </RowCellStyled>
                </RowStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.clocked')}</RowCellStyled>
                  <RowCellStyled>
                    {(() => {
                      // Get actual clock-in time from attendance data
                      if (!attendanceData || !date || !employee) return '--:--'

                      const dayAttendance = attendanceData[date]
                      if (!dayAttendance) return '--:--'

                      const employeeAttendance = dayAttendance[employee.uid]
                      if (!employeeAttendance) return '--:--'

                      // Find the current shift's attendance data
                      const currentShiftKey = (currentShift as any)?.shiftKey
                      if (!currentShiftKey) return '--:--'

                      const attendanceShift = employeeAttendance[currentShiftKey]
                      if (!attendanceShift || !attendanceShift.start) return '--:--'

                      const clockInMinutes = attendanceShift.start
                      return `${Math.floor(clockInMinutes / 60)}:${(clockInMinutes % 60).toString().padStart(2, '0')}`
                    })()}
                  </RowCellStyled>
                  <RowCellStyled>
                    {(() => {
                      // Get actual clock-out time from attendance data
                      if (!attendanceData || !date || !employee) return '--:--'

                      const dayAttendance = attendanceData[date]
                      if (!dayAttendance) return '--:--'

                      const employeeAttendance = dayAttendance[employee.uid]
                      if (!employeeAttendance) return '--:--'

                      // Find the current shift's attendance data
                      const currentShiftKey = (currentShift as any)?.shiftKey
                      if (!currentShiftKey) return '--:--'

                      const attendanceShift = employeeAttendance[currentShiftKey]
                      if (!attendanceShift || !attendanceShift.end) return '--:--'

                      const clockOutMinutes = attendanceShift.end
                      return `${Math.floor(clockOutMinutes / 60)}:${(clockOutMinutes % 60).toString().padStart(2, '0')}`
                    })()}
                  </RowCellStyled>
                  <RowCellStyled>
                    {notClockedOut && (
                      <WarningTooltipButton
                        tooltipText={I18n.t(
                          'payroll.employee_did_not_clock_out'
                        )}
                        show={showClockedTooltip}
                        canShow={canShowTooltip}
                        onToggleTooltip={() =>
                          setShowClockedTooltip(!showClockedTooltip)
                        }
                        onHide={() => setShowClockedTooltip(false)}
                        buttonRef={clockedTooltipRef}
                      />
                    )}
                  </RowCellStyled>
                </RowStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.rounded')}</RowCellStyled>
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setRoundedTimeStart(value)
                      // Update local state when value changes
                      if (activeShift !== null) {
                        const newStartMinutes = value ? value.hour() * 60 + value.minute() : undefined
                        const currentStartMinutes = currentShift?.start
                        if (newStartMinutes !== currentStartMinutes) {
                          // Preserve the existing end time when updating start time
                          const updates: Partial<AttendanceShift> = { start: newStartMinutes }
                          if (currentShift?.end) {
                            updates.end = currentShift.end
                          }
                          updateLocalShift(activeShift, updates)
                        }
                      }
                    }}
                    value={roundedTimeStart}
                    placeholder='-:-'
                    hideArrow
                    $noValue={!roundedTimeStart}
                    isFixedMenu
                  />
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setRoundedTimeEnd(value)
                      // Update local state when value changes
                      if (activeShift !== null) {
                        const newEndMinutes = value ? value.hour() * 60 + value.minute() : undefined
                        const currentEndMinutes = currentShift?.end
                        if (newEndMinutes !== currentEndMinutes) {
                          // Preserve the existing start time when updating end time
                          const updates: Partial<AttendanceShift> = { end: newEndMinutes }
                          if (currentShift?.start) {
                            updates.start = currentShift.start
                          }
                          updateLocalShift(activeShift, updates)
                        }
                      }
                    }}
                    value={roundedTimeEnd}
                    placeholder='-:-'
                    hideArrow
                    $noValue={!roundedTimeEnd}
                    isFixedMenu
                  />
                  <RowCellStyled>
                    {/* Removed short shift validation - managers can book any length shifts */}
                    {(() => {

                      return isOverlap && (
                        <WarningTooltipButton
                          isRed
                          tooltipText={
                            hoursTableAnalysis?.issues?.join(', ') ||
                            'Shift overlaps detected - please resolve conflicts'
                          }
                          show={showOverlapTooltip}
                          canShow={canShowTooltip}
                          onToggleTooltip={() =>
                            setShowOverlapTooltip(!showOverlapTooltip)
                          }
                          onHide={() => setShowOverlapTooltip(false)}
                          buttonRef={overlapTooltipRef}
                        />
                      )
                    })()}
                    {totalHours}{I18n.t('common.hours_shorten').toLowerCase()}
                    <DeleteButtonStyled
                      onClick={() => {
                        setShowDeleteShiftModal(true)
                        setShowClockedTooltip(false)
                        // Removed setShowShortShiftTooltip - shift length validation disabled
                        setShowOverlapTooltip(false)
                        setShowPlannedTooltip(false)
                        setCanShowTooltip(false)
                      }}
                    >
                      <DeleteIconStyled />
                    </DeleteButtonStyled>
                  </RowCellStyled>
                </RowStyled>
              </ShiftBlockStyled>
              <DividerStyled />

              <BreakBlockStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.break')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.start')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('common.end')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.total')}</RowCellStyled>
                </RowStyled>

                {/* Render multiple break rows dynamically */}
                {(() => {
                  const breaks = currentShift?.breaks || {}
                  const breakEntries = Object.entries(breaks)
                  


                  // Always ensure at least 1 break row exists
                  if (breakEntries.length === 0) {
                    // Create a default break entry for new shifts or shifts without breaks
                    const defaultBreakId = 'break_1'

                    return (
                      <RowStyled key={defaultBreakId}>
                        <RowCellStyled>
                          {I18n.t('payroll.unpaid')}
                          <CustomCheckboxStyled
                            $isActive={false}
                            onClick={() => {
                              if (activeShift !== null) {
                                const updatedBreaks = {
                                  [defaultBreakId]: {
                                    start: 0,
                                    end: 0,
                                    lengthRounded: 0,
                                    isUnpaid: false
                                  }
                                }
                                updateLocalShift(activeShift, { breaks: updatedBreaks })
                              }
                            }}
                          />
                        </RowCellStyled>
                        <CustomTimePickerStyled
                          minuteStep={1}
                          onChange={value => {
                            if (activeShift !== null) {
                              const startMinutes = value ? value.hour() * 60 + value.minute() : 0
                              // Create the break with proper start time and preserve any existing end time
                              const existingBreaks = currentShift?.breaks || {}
                              const existingBreak = existingBreaks[defaultBreakId]
                              const endMinutes = existingBreak?.end || 0
                              const lengthRounded = Math.max(0, endMinutes - startMinutes)

                              const updatedBreaks = {
                                ...existingBreaks,
                                [defaultBreakId]: {
                                  start: startMinutes,
                                  end: endMinutes,
                                  lengthRounded,
                                  isUnpaid: true
                                }
                              }
                              updateLocalShift(activeShift, { breaks: updatedBreaks })
                            }
                          }}
                          value={null}
                          placeholder='-:-'
                          hideArrow
                          $noValue={true}
                          isFixedMenu
                        />
                        <CustomTimePickerStyled
                          minuteStep={1}
                          onChange={value => {
                            if (activeShift !== null) {
                              const endMinutes = value ? value.hour() * 60 + value.minute() : 0
                              // Create the break with proper end time and preserve any existing start time
                              const existingBreaks = currentShift?.breaks || {}
                              const existingBreak = existingBreaks[defaultBreakId]
                              const startMinutes = existingBreak?.start || 0
                              const lengthRounded = Math.max(0, endMinutes - startMinutes)

                              const updatedBreaks = {
                                ...existingBreaks,
                                [defaultBreakId]: {
                                  start: startMinutes,
                                  end: endMinutes,
                                  lengthRounded,
                                  isUnpaid: true
                                }
                              }
                              updateLocalShift(activeShift, { breaks: updatedBreaks })
                            }
                          }}
                          value={null}
                          placeholder='-:-'
                          hideArrow
                          $noValue={true}
                          isFixedMenu
                        />
                        <RowCellStyled>
                          0{I18n.t('common.minutes_shorten').toLowerCase()}
                        </RowCellStyled>
                      </RowStyled>
                    )
                  }

                  // Render existing breaks with delete functionality
                  return breakEntries.map(([breakId, breakData], index) => {
                    // Get the break data from currentShift to ensure we have the latest local changes
                    const currentBreakData = currentShift?.breaks?.[breakId] || breakData
                    // Determine if this specific break is unpaid using current data
                    const isThisBreakUnpaid = (currentBreakData as any).isUnpaid !== false // Default to true if not explicitly set to false



                    return (
                      <RowStyled key={`${breakId}-${index}`}>
                        <RowCellStyled>
                          {!isThisBreakUnpaid
                            ? I18n.t('payroll.paid')
                            : I18n.t('payroll.unpaid')}
                          <CustomCheckboxStyled
                            $isActive={!isThisBreakUnpaid}
                            onClick={() => {
                              if (activeShift !== null) {
                                const updatedBreaks = {
                                  ...currentShift?.breaks,
                                  [breakId]: {
                                    ...currentBreakData,
                                    isUnpaid: !isThisBreakUnpaid
                                  }
                                }
                                console.log('🔄 Toggling break paid status:', { breakId, wasUnpaid: isThisBreakUnpaid, nowUnpaid: !isThisBreakUnpaid, updatedBreaks })
                                updateLocalShift(activeShift, { breaks: updatedBreaks })
                              }
                            }}
                          />
                        </RowCellStyled>
                        <CustomTimePickerStyled
                          minuteStep={1}
                          onChange={value => {
                            if (activeShift !== null) {
                              const startMinutes = value ? value.hour() * 60 + value.minute() : 0
                              const endMinutes = currentBreakData.end || 0
                              const lengthRounded = Math.max(0, endMinutes - startMinutes)
                              const updatedBreaks = {
                                ...currentShift?.breaks,
                                [breakId]: {
                                  ...currentBreakData,
                                  start: startMinutes,
                                  lengthRounded
                                }
                              }
                              console.log('⏰ Updating break start time:', { breakId, startMinutes, lengthRounded, updatedBreaks })
                              updateLocalShift(activeShift, { breaks: updatedBreaks })
                            }
                          }}
                          value={currentBreakData.start && currentBreakData.start > 0 ? moment().hour(Math.floor(currentBreakData.start / 60)).minute(currentBreakData.start % 60) : null}
                          placeholder='-:-'
                          hideArrow
                          $noValue={!currentBreakData.start || currentBreakData.start === 0}
                          isFixedMenu
                        />
                        <CustomTimePickerStyled
                          minuteStep={1}
                          onChange={value => {
                            if (activeShift !== null) {
                              const endMinutes = value ? value.hour() * 60 + value.minute() : 0
                              const startMinutes = currentBreakData.start || 0
                              const lengthRounded = Math.max(0, endMinutes - startMinutes)
                              const updatedBreaks = {
                                ...currentShift?.breaks,
                                [breakId]: {
                                  ...currentBreakData,
                                  end: endMinutes,
                                  lengthRounded
                                }
                              }
                              console.log('⏰ Updating break end time:', { breakId, endMinutes, lengthRounded, updatedBreaks })
                              updateLocalShift(activeShift, { breaks: updatedBreaks })
                            }
                          }}
                          value={currentBreakData.end && currentBreakData.end > 0 ? moment().hour(Math.floor(currentBreakData.end / 60)).minute(currentBreakData.end % 60) : null}
                          placeholder='-:-'
                          hideArrow
                          $noValue={!currentBreakData.end || currentBreakData.end === 0}
                          isFixedMenu
                        />
                        <RowCellStyled>
                          {currentBreakData.lengthRounded || 0}
                          {I18n.t('common.minutes_shorten').toLowerCase()}
                          <DeleteButtonStyled
                            ref={el => (deleteBreakRefs.current[breakId] = el)}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              // Show tooltip instead of directly deleting
                              setShowDeleteBreakTooltip(showDeleteBreakTooltip === breakId ? null : breakId);
                            }}
                          >
                            <DeleteIconStyled />
                          </DeleteButtonStyled>
                        </RowCellStyled>
                      </RowStyled>
                    )
                  })
                })()}

                <AddBreakButtonStyled
                  onClick={() => {
                    // Add a new break row using local state
                    if (activeShift !== null && currentShift) {
                      const existingBreaks = currentShift.breaks || {}
                      const breakCount = Object.keys(existingBreaks).length
                      const newBreakId = `break_${Date.now()}_${breakCount + 1}`

                      const updatedBreaks = {
                        ...existingBreaks,
                        [newBreakId]: {
                          start: 0,
                          end: 0,
                          lengthRounded: 0,
                          isUnpaid: true
                        }
                      }

                      console.log('➕ Adding new break:', { newBreakId, updatedBreaks })
                      updateLocalShift(activeShift, { breaks: updatedBreaks })
                    }
                  }}
                >
                  {I18n.t('payroll.add_break')} <PlusIconStyled />
                </AddBreakButtonStyled>
              </BreakBlockStyled>
            </ScrollBlockStyled>

            <DividerStyled />

            <TotalBlockStyled>
              <TotalBlockLabelStyled>
                {I18n.t('payroll.total')}
              </TotalBlockLabelStyled>
              <TotalBlockValueStyled>
                {totalAllShiftsHours}{I18n.t('common.hours_shorten').toLowerCase()}
              </TotalBlockValueStyled>
              <TotalBlockValueStyled>
                {(() => {
                  // Calculate total pay based on hours and rate type
                  // Use original shifts with unsaved changes applied
                  const allShifts = [...shifts]
                  
                  // Apply unsaved changes to each shift
                  const shiftsWithChanges = allShifts.map((shift, index) => {
                    const changes = unsavedChanges[index] || {}
                    return { ...shift, ...changes }
                  })
                  
                  const totalHours = shiftsWithChanges.reduce((total, shift) => {
                    if (!shift.start || !shift.end) return total

                    let shiftMinutes = shift.end - shift.start
                    if (shiftMinutes < 0) shiftMinutes += 24 * 60

                    const breakMinutes = Object.values(shift.breaks || {}).reduce((breakTotal, breakItem) => {
                      // Only subtract unpaid breaks from total hours for pay calculation
                      const isUnpaid = (breakItem as any).isUnpaid !== false // Default to true if not explicitly set to false
                      return breakTotal + (isUnpaid ? (breakItem.lengthRounded || 0) : 0)
                    }, 0)

                    const workMinutes = shiftMinutes - breakMinutes
                    return total + (workMinutes / 60)
                  }, 0)

                  const numericRate = Number(rate) || 0
                  let totalPay = 0

                  if (type === 'hourly') {
                    // For hourly employees: hours * hourly rate
                    totalPay = Math.max(0, totalHours * numericRate)
                  } else {
                    // For yearly salary employees: yearly salary / 365 (daily rate)
                    totalPay = Math.max(0, numericRate / 365)
                  }

                  return (
                    <NumberFormatted value={totalPay} />
                  )
                })()}
              </TotalBlockValueStyled>
            </TotalBlockStyled>

            <ButtonBlockStyled>
              {hoursTableAnalysis && (() => {
                const buttonStates = getButtonStatesFromShiftAnalysis(hoursTableAnalysis)

                return (
                  <>
                    <ButtonWrapStyled>
                      {buttonStates.showReclaim && (
                        <OverlayTrigger
                          trigger={['hover', 'focus']}
                          placement='top-start'
                          overlay={
                            <TooltipStyled
                              id='shift-popover_reclaim-tooltip'
                              $isReclaimTooltip
                            >
                              {I18n.t('payroll.you_save')} ${saveAmount}{' '}
                              {I18n.t('payroll.with_planned_start_time')}
                            </TooltipStyled>
                          }
                        >
                          <OrangeButtonStyled
                            color='orange'
                            onClick={() => {
                              onChangeStatus('claim', () =>
                                setHasClaimed(!hasClaimed)
                              )
                              setHasClaimed(!hasClaimed)
                              setUnsavedChanges({})
                            }}
                            $isActive={hasClaimed}
                          >
                            <p>
                              {I18n.t('payroll.reclaim')}{' '}
                              <NumberFormatted value={saveAmount} />
                            </p>
                            <p>
                              {I18n.t('payroll.saved')}{' '}
                              <NumberFormatted value={saveAmount} />
                            </p>
                          </OrangeButtonStyled>
                        </OverlayTrigger>
                      )}
                    </ButtonWrapStyled>
                    <ButtonWrapStyled>
                      {/* Always show approve button - remove blocking conditions */}
                      <SaveButtonStyled
                        color={buttonStates.approveButtonColor || 'green'}
                        onClick={async () => {
                          if (hasClaimed) {
                            setHasClaimed(!hasClaimed)
                          } else {
                            try {
                              // For new shifts, mark as approved before saving
                              if (activeShift !== null && activeShift >= shifts.length) {
                                updateLocalShift(activeShift, { isConfirmed: true })
                              }
                              await saveLocalChanges() // Save any unsaved changes first
                              toast.success('Shift approved successfully!')
                              onClose()
                            } catch (error) {
                              // Error is already handled in saveLocalChanges
                            }
                          }
                        }}
                        $isActive={hasClaimed}
                        $conflictColor={mapColorToConflictType(getShiftStatusColor(hoursTableAnalysis.status))}
                      >
                        <span>{I18n.t('common.approve')}</span>
                        <span>{I18n.t('common.undo')}</span>
                      </SaveButtonStyled>
                    </ButtonWrapStyled>
                  </>
                )
              })()}

              {/* Fallback for when analysis is not available */}
              {!hoursTableAnalysis && (
                <>
                  <ButtonWrapStyled>
                    {canReclaim && (
                      <OverlayTrigger
                        trigger={['hover', 'focus']}
                        placement='top-start'
                        overlay={
                          <TooltipStyled
                            id='shift-popover_reclaim-tooltip'
                            $isReclaimTooltip
                          >
                            {I18n.t('payroll.you_save')} ${saveAmount}{' '}
                            {I18n.t('payroll.with_planned_start_time')}
                          </TooltipStyled>
                        }
                      >
                        <OrangeButtonStyled
                          color='orange'
                          onClick={ () =>
                          {
                            if ( hasClaimed )
                            {
                              setHasClaimed( !hasClaimed );
                            } else
                            {
                              onChangeStatus( 'approve' );
                              onClose();
                            }
                          } }
                          $isActive={hasClaimed}
                        >
                          <p>
                            {I18n.t('payroll.reclaim')}{' '}
                            <NumberFormatted value={saveAmount} />
                          </p>
                          <p>
                            {I18n.t('payroll.saved')}{' '}
                            <NumberFormatted value={saveAmount} />
                          </p>
                        </OrangeButtonStyled>
                      </OverlayTrigger>
                    )}
                  </ButtonWrapStyled>
                  <ButtonWrapStyled>
                    {canReclaim ? (
                      <SaveButtonStyled
                        color='green'
                        onClick={async () => {
                          if (hasClaimed) {
                            setHasClaimed(!hasClaimed)
                          } else {
                            try {
                              // For new shifts, mark as approved before saving
                              if (activeShift !== null && activeShift >= shifts.length) {
                                updateLocalShift(activeShift, { isConfirmed: true })
                              }
                              await saveLocalChanges() // Save any unsaved changes first
                              toast.success('Shift approved successfully!')
                              onClose()
                            } catch (error) {
                              // Error is already handled in saveLocalChanges
                            }
                          }
                        }}
                        $isActive={hasClaimed}
                      >
                        <span>{I18n.t('common.approve')}</span>
                        <span>{I18n.t('common.undo')}</span>
                      </SaveButtonStyled>
                    ) : (
                      <GreyButtonStyled onClick={async () => {
                        try {
                          await saveLocalChanges() // Save local changes instead of using handleSaveShift
                          toast.success('Shift saved successfully!')
                          onClose()
                        } catch (error) {
                          toast.error('Failed to save shift')
                        }
                      }}>
                        {I18n.t('common.save')}
                      </GreyButtonStyled>
                    )}
                  </ButtonWrapStyled>
                </>
              )}
            </ButtonBlockStyled>
          </ContainerStyled>
        </PopoverStyled>
        <Overlay
          rootClose
          show={showActivityPopover}
          placement={showActivityPopoverOnLeft ? 'left-start' : 'right-start'}
          target={() => activityPopoverRef.current}
          onHide={() => setShowActivityPopover(false)}
        >
          <ActivitiesPopover
            onClose={() => setShowActivityPopover(false)}
            employee={employee}
            date={date}
            currentCompany={currentCompany}
          />
        </Overlay>
        <Overlay
          show={showRoleTooltip && canShowTooltip}
          placement='top'
          target={() => roleDropdownRef.current}
          onHide={() => setShowRoleTooltip(false)}
        >
          <TooltipStyled
            $isRed={true}
            onClick={(e: React.MouseEvent<HTMLDivElement>) => e.stopPropagation()}
            id='payroll_shift-popover-role-tooltip'
          >
            {I18n.t('payroll.please_select_role') || 'Please select a role before approving'}
            <CloseButtonTooltipStyled onClick={() => setShowRoleTooltip(false)}>
              <CloseIconStyled />
            </CloseButtonTooltipStyled>
          </TooltipStyled>
        </Overlay>
        
        {/* Delete Break Confirmation Tooltip */}
        {showDeleteBreakTooltip && (
          <Overlay
            show={!!showDeleteBreakTooltip}
            placement='top'
            target={() => deleteBreakRefs.current[showDeleteBreakTooltip]}
            onHide={() => setShowDeleteBreakTooltip(null)}
            rootClose
          >
            <TooltipStyled
              $isRed={true}
              onClick={(e: React.MouseEvent<HTMLDivElement>) => e.stopPropagation()}
              id='payroll_delete-break-tooltip'
            >
              <CloseButtonTooltipStyled onClick={() => setShowDeleteBreakTooltip(null)}>
                <CloseIconStyled />
              </CloseButtonTooltipStyled>
              {I18n.t('payroll.are_you_sure_delete_break')}
              <div style={{ marginTop: '0.8rem', display: 'flex', justifyContent: 'center' }}>
                <TooltipDeleteButtonStyled
                  color='red'
                  onClick={() => showDeleteBreakTooltip && handleDeleteBreak(showDeleteBreakTooltip)}
                >
                  {I18n.t('common.delete')}
                </TooltipDeleteButtonStyled>
              </div>
            </TooltipStyled>
          </Overlay>
        )}
      </>
    )
  }
)

type WarningTooltipButtonProps = {
  isRed?: boolean
  tooltipText: string
  show: boolean
  canShow: boolean
  onToggleTooltip: () => void
  onHide: () => void
  buttonRef: React.RefObject<HTMLButtonElement>
}

const WarningTooltipButton = ({
  isRed,
  tooltipText,
  show,
  canShow,
  onToggleTooltip,
  onHide,
  buttonRef
}: WarningTooltipButtonProps) => (
  <>
    <WarningButtonStyled
      $isRed={isRed}
      onClick={() => canShow && onToggleTooltip()}
      ref={buttonRef}
    >
      <WarningIconStyled />
    </WarningButtonStyled>
    <Overlay
      show={show && canShow}
      placement='top'
      target={() => buttonRef.current}
      onHide={onHide}
    >
      <TooltipStyled
        $isRed={isRed}
        onClick={(e: React.MouseEvent<HTMLDivElement>) => e.stopPropagation()}
        id='payroll_shift-popover-tooltip'
      >
        {tooltipText}
        <CloseButtonTooltipStyled onClick={onHide}>
          <CloseIconStyled />
        </CloseButtonTooltipStyled>
      </TooltipStyled>
    </Overlay>
  </>
)

export default ShiftPopover
