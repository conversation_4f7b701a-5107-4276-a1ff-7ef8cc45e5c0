import React, { useRef, useState } from 'react'
import Overlay from 'react-bootstrap/Overlay'
import useOnclickOutside from 'react-cool-onclickoutside'
import { I18n } from 'react-redux-i18n'

import dayjs, { Dayjs } from 'dayjs'

import NumberFormatted from 'components/ui/NumberFormatted'

import {
  TabButtonStyled,
  WeekPeriodTabsStyled,
  ClockIconStyled,
  TableBlockStyled,
  TableHeaderStyled,
  OptionBlockStyled,
  TotalEmployeesStyled,
  InputWrapStyled,
  InputStyled,
  SearchIconStyled,
  FilterButtonStyled,
  FilterIconStyled,
  SummaryBlockStyled,
  TotalHourStyled,
  TotalSalaryStyled,
  TableStyled,
  TableRowStyled,
  HeaderStyled,
  ArrowDownIconStyled,
  HeaderTitleStyled,
  BodyStyled,
  ItemStyled,
  ItemHeaderStyled,
  EmployeeInfoStyled,
  AvatarInitialsStyled,
  EmployeeInfoColumnStyled,
  EmployeeNameStyled,
  EmployeeIdStyled,
  EmployeeStatsStyled,
  EmployeeStatsBlockStyled,
  EmployeeStatsBlockItemStyled,
  ItemBodyStyled,
  DayItemStyled,
  DayItemHeaderStyled,
  DayItemTitleStyled,
  AddShiftButtonStyled,
  PlusIconStyled,
  DayItemBodyStyled,
  DayItemBodyLabelBlockStyled,
  LabelStyled,
  LabelBlockHeadStyled,
  TimeBlockStyled,
  DayItemShiftStyled,
  TimeBlockValueStyled,
  TimeBlockBreakValueStyled
} from '../../styles/HoursTable.styles'

import {
  DepartmentRoles,
  DepartmentType,
  RoleFilterItem,
  RoleFilterState,
  filterEmployeesByRoles
} from 'utils/payroll/roleFilterUtils'
import {
  analyzeShiftStatus,
  getShiftStatusColor,
  getShiftStatusDescription
} from 'utils/payroll/shiftStatusAnalysis'
import { containsString } from 'utils/removeDiacriticsString'
import { addZeros } from 'routes/PayrollOld/payrollUtils'

import { AttendanceShift, AttendanceShifts } from 'types/attendance'
import { Company, IPosition } from 'types/company'
import { IEmployee, IEmployeePosition } from 'types/employee'

import arrowDownIcon from 'img/icons/arrowDownEqualIcon.svg'
import FilterDrawer from './FilterDrawer'
import ShiftPopover from './ShiftPopover'

// Helper to get highest priority status and color for a day's shifts
function getDayStatusAndColor(
  dayShifts: AttendanceShift[],
  employeeId: string,
  date: string,
  attendanceData: AttendanceShifts,
  currentCompany: Company,
  isToday: boolean
) {
  let highestPriority = 0
  let dayStatus = 'approved'
  dayShifts.forEach(shift => {
    const shiftAnalysis = analyzeShiftStatus(
      shift,
      employeeId,
      date,
      attendanceData,
      currentCompany,
      isToday
    )
    if (shiftAnalysis.priority > highestPriority) {
      highestPriority = shiftAnalysis.priority
      dayStatus = shiftAnalysis.status
      console.log('lie shiftAnalysis', shiftAnalysis)
    }
  })
  const dayStatusColor = getShiftStatusColor( dayStatus as any )
  
  return { dayStatus, dayStatusColor }
}

interface HoursTableProps {
  employeesArray: Array<{
    id: number
    name: string
    surname: string
    avatar: string
    userId: string
    uid: string
    positions?: IEmployeePosition[]
    payrollId?: string
    customId?: string
  }>
  employeesByRole: {
    [roleId: string]: { role: IPosition; employees: IEmployee[] }
  }
  searchEmployee: string
  onSearchEmployee: (value: string) => void
  displayBy: string
  setDisplayBy: (value: string) => void
  displayByArray: Array<{ id: string; label: string; icon: React.ReactNode }>
  attendanceData: AttendanceShifts
  currentCompany: Company
  isDataLoaded: boolean
  selectedPositionId: string
  setSelectedPositionId: (value: string) => void
  startOfPeriod: Dayjs
  currentPeriodOffset: number
  payrollLength: number
  onSave: (
    newShift: { [key: string]: AttendanceShift },
    employeeId: string,
    date: string
  ) => void
  onDeleteShift: (shiftKey: string, employeeId: string, date: string) => void
  departmentRoles: DepartmentRoles
  roleFilterState: RoleFilterState
  onRoleFilterChange: (newState: RoleFilterState) => void
  isSingleRole: boolean
  singleRoleInfo?: {
    department: DepartmentType
    role: RoleFilterItem
  } | null
  hasPayrollIntegration: boolean
}

// Helper function to get consistent employee ID
const getEmployeeId = (
  employee: IEmployee | { uid: string; userId?: string; [key: string]: unknown }
): string => {
  // Try uid first, then userId, then fallback to empty string
  const id = employee.uid || ('userId' in employee ? employee.userId : '') || ''
  // Ensure we return a string and handle null/undefined cases
  return id ? String(id) : ''
}

// Helper function to calculate employee weekly stats
const getEmployeeWeeklyStats = (
  employeeUid: string,
  attendanceData: AttendanceShifts,
  startOfPeriod: Dayjs,
  payrollLength: number
) => {
  // startOfPeriod is already the correct period start (offset already applied in parent)
  const currentPeriodStart = startOfPeriod.clone()
  const numberOfWeeks = payrollLength === 14 ? 2 : 1

  const weeklyStats = []
  for (let weekIndex = 0; weekIndex < numberOfWeeks; weekIndex++) {
    // Calculate week start based on the period start + (weekIndex * 7 days)
    // This ensures we follow the payroll period exactly, not calendar weeks
    const weekStart = currentPeriodStart.clone().add(weekIndex * 7, 'days')
    const weekEnd = weekStart.clone().add(6, 'days')

    let weekHours = 0
    let weekSalary = 0

    // Iterate through each day in the week
    let currentDay = weekStart.clone()
    while (currentDay.isSameOrBefore(weekEnd)) {
      const dateKey = currentDay.format('YYYY-MM-DD')
      const dayShifts = attendanceData[dateKey]

      if (dayShifts && dayShifts[employeeUid]) {
        for (const shift of Object.values(dayShifts[employeeUid])) {
          const shiftData = shift as AttendanceShift & {
            shiftLengthHours?: number
            salary?: number
          }

          // Calculate hours
          if (shiftData.shiftLengthHours) {
            weekHours += shiftData.shiftLengthHours
          } else if (shiftData.start && shiftData.end) {
            const shiftHours = (shiftData.end - shiftData.start) / 60
            weekHours += shiftHours > 0 ? shiftHours : 0
          }

          // Add salary
          if (shiftData.salary) {
            weekSalary += shiftData.salary
          }
        }
      }

      currentDay = currentDay.add(1, 'day')
    }

    weeklyStats.push({
      weekStart: weekStart.format('YYYY-MM-DD'),
      weekEnd: weekEnd.format('YYYY-MM-DD'),
      hours: weekHours,
      salary: weekSalary
    })
  }



  return { weeklyStats }
}

// Helper function to get shifts for a specific employee and date
const getEmployeeShiftsForDate = (
  employeeUid: string,
  date: string,
  attendanceData: AttendanceShifts
) => {
  const dayShifts = attendanceData[date]
  if (!dayShifts || !dayShifts[employeeUid]) {
    return []
  }

  return Object.entries(dayShifts[employeeUid]).map(([shiftKey, shift]) => ({
    ...shift,
    shiftKey
  }))
}

// Helper function to format time from minutes to HH:MM
const formatTime = (minutes: number | undefined | null): string => {
  if (minutes === undefined || minutes === null) return '--:--'
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

// Helper function to calculate break duration
const calculateBreakDuration = (
  breaks: { [key: string]: { start?: number; end?: number; lengthRounded?: number } } | undefined
): number => {
  if (!breaks || typeof breaks !== 'object') return 0

  let totalBreakTime = 0
  Object.values(breaks).forEach(breakItem => {
    // Use lengthRounded if available (this is the saved value), otherwise calculate from start/end
    if (breakItem.lengthRounded !== undefined && breakItem.lengthRounded > 0) {
      totalBreakTime += breakItem.lengthRounded
    } else if (breakItem.start && breakItem.end) {
      totalBreakTime += breakItem.end - breakItem.start
    }
  })

  return totalBreakTime
}

// Helper function to render a single shift with status analysis and styling
const renderShiftItem = (
  shift: AttendanceShift & {
    isClockInDifferent?: boolean
    isClockOutDiffrent?: boolean
  },
  shiftId: string,
  employeeId: string,
  date: string,
  isToday: boolean,
  attendanceData: AttendanceShifts,
  currentCompany: Company,
  shiftRefs: React.MutableRefObject<Map<string, HTMLElement | null>>,
  setActiveShiftPopover: (id: string) => void
) => {
  const breakDuration = calculateBreakDuration(shift.breaks)

  // Enhanced shift status analysis
  const shiftAnalysis = analyzeShiftStatus(
    shift,
    employeeId,
    date,
    attendanceData,
    currentCompany,
    isToday
  )
  const statusColor = getShiftStatusColor(shiftAnalysis.status)

  return (
    <DayItemShiftStyled key={shiftId}>
      <TimeBlockStyled
        ref={el => {
          shiftRefs.current.set(shiftId, el)
        }}
        onClick={() => {
          setActiveShiftPopover(shiftId)
        }}
        title={`${getShiftStatusDescription(shiftAnalysis.status)}${
          shiftAnalysis.issues.length > 0 
            ? ': ' + shiftAnalysis.issues.join(', ') 
            : ''
        }`}
        $isBlue={statusColor === 'blue'}
        $isWhite={statusColor === 'white'}
        $isGrey={statusColor === 'grey'}
        $isOrange={statusColor === 'orange'}
        $isRed={statusColor === 'red'}
      >
        {statusColor === 'red' ? (
          <>
            {/* For red (overlapping) shifts, apply granular styling */}
            <TimeBlockValueStyled
              $isRed={!shiftAnalysis.conflictField || shiftAnalysis.conflictField === 'start'}
            >
              {formatTime(shift.start)}
            </TimeBlockValueStyled>
            <TimeBlockValueStyled
              $isRed={!shiftAnalysis.conflictField || shiftAnalysis.conflictField === 'end'}
            >
              {formatTime(shift.end)}
            </TimeBlockValueStyled>
          </>
        ) : (
          <>
            {/* For all other colors, use normal styling */}
            <TimeBlockValueStyled>
              {formatTime(shift.start)}
            </TimeBlockValueStyled>
            <TimeBlockValueStyled>
              {formatTime(shift.end)}
            </TimeBlockValueStyled>
          </>
        )}
      </TimeBlockStyled>
      <TimeBlockBreakValueStyled>
        {breakDuration > 0 ? Math.round(breakDuration) : '--'}{' '}
        {breakDuration > 0 ? I18n.t('common.minutes_shorten').toLowerCase() : ''}
      </TimeBlockBreakValueStyled>
    </DayItemShiftStyled>
  )
}

const HoursTable = ({
  employeesArray,
  employeesByRole,
  searchEmployee,
  onSearchEmployee,
  displayBy,
  setDisplayBy,
  displayByArray,
  attendanceData,
  currentCompany,
  isDataLoaded,
  selectedPositionId,
  setSelectedPositionId,
  startOfPeriod,
  currentPeriodOffset,
  payrollLength,
  onSave,
  onDeleteShift,
  departmentRoles,
  roleFilterState,
  onRoleFilterChange,
  isSingleRole,
  singleRoleInfo,
  hasPayrollIntegration
}: HoursTableProps) => {
  const [expandedRows, setExpandedRows] = useState<{ [id: string]: boolean }>(
    {}
  )
  const [showFilterDrawer, setShowFilterDrawer] = useState(false)

  // Generate days array for the current period
  const daysArray = React.useMemo(() => {
    // startOfPeriod is already the correct period start (offset already applied in parent)
    const currentPeriodStart = startOfPeriod.clone()
    const days = []

    for (let i = 0; i < payrollLength; i++) {
      const day = currentPeriodStart.clone().add(i, 'days')
      days.push({
        date: day.format('YYYY-MM-DD'),
        dayName: day.format('ddd'),
        dayNumber: day.format('D'),
        isToday: day.isSame(dayjs(), 'day')
      })
    }

    return days
  }, [startOfPeriod, payrollLength])

  const filteredEmployees = React.useMemo(() => {
    // First filter by search term
    let filtered = employeesArray.filter(employee => {
      const employeeName = employee.name + ' ' + employee.surname
      return containsString(employeeName, searchEmployee)
    })

    // Apply role filtering only for "By Roles" view
    if (displayBy === 'role') {
      filtered = filterEmployeesByRoles(
        filtered,
        roleFilterState,
        currentCompany.jobs || {}
      )
    }

    // Apply specific position filter if selected
    if (selectedPositionId) {
      filtered = filtered.filter(employee =>
        (employee.positions || []).some(
          (pos: IEmployeePosition) =>
            pos.subcategoryId === selectedPositionId ||
            pos.categoryId === selectedPositionId
        )
      )
    }

    // Sort by first name to maintain alphabetical order
    filtered.sort((a, b) => {
      const aName = a.name || ''
      const bName = b.name || ''
      return aName.localeCompare(bName)
    })

    return filtered
  }, [
    employeesArray,
    searchEmployee,
    displayBy,
    roleFilterState,
    selectedPositionId,
    currentCompany.jobs
  ])

  // Calculate total hours and salary for the current period
  const { totalHours, totalSalary } = React.useMemo(() => {
    let hours = 0
    let salary = 0

    const currentPeriodStart = startOfPeriod
      .clone()
      .add(currentPeriodOffset * payrollLength, 'days')
    const currentPeriodEnd = currentPeriodStart
      .clone()
      .add(payrollLength - 1, 'days')

    // Iterate through each day in the current period
    let currentDay = currentPeriodStart.clone()
    while (currentDay.isSameOrBefore(currentPeriodEnd)) {
      const dateKey = currentDay.format('YYYY-MM-DD')
      const dayShifts = attendanceData[dateKey]

      if (dayShifts) {
        // Calculate for all employees on this day
        for (const employee of filteredEmployees) {
          const employeeShifts = dayShifts[employee.uid]
          if (employeeShifts) {
            for (const shift of Object.values(employeeShifts)) {
              const shiftData = shift as AttendanceShift & {
                shiftLengthHours?: number
                salary?: number
              }
              // Calculate hours from start/end times or use shiftLengthHours if available
              if (shiftData.shiftLengthHours) {
                hours += shiftData.shiftLengthHours
              } else if (shiftData.start && shiftData.end) {
                const shiftHours = (shiftData.end - shiftData.start) / 60
                hours += shiftHours > 0 ? shiftHours : 0
              }

              // Add salary if available
              if (shiftData.salary) {
                salary += shiftData.salary
              }
            }
          }
        }
      }

      currentDay = currentDay.add(1, 'day')
    }

    return { totalHours: hours, totalSalary: salary }
  }, [
    attendanceData,
    filteredEmployees,
    startOfPeriod,
    currentPeriodOffset,
    payrollLength
  ])

  // Filter employeesByRole based on search term, role filter, and position filter
  const filteredEmployeesByRole = React.useMemo(() => {
    const filtered: {
      [roleId: string]: { role: IPosition; employees: IEmployee[] }
    } = {}

    Object.entries(employeesByRole).forEach(([roleId, roleData]) => {
      const job = currentCompany.jobs?.[roleId]
      if (!job || job.archived) return

      // Check if this role is selected in the filter
      const department = job.type || 'FOH'
      const isRoleSelected =
        roleFilterState.selectedDepartments.includes(department) &&
        roleFilterState.selectedRoles[department]?.includes(roleId)

      // Only include roles that are selected in the filter
      if (!isRoleSelected) {
        return // Skip this role entirely if not selected
      }

      // First filter by search term
      let filteredRoleEmployees = roleData.employees.filter(employee => {
        const employeeName = employee.name + ' ' + employee.surname
        return containsString(employeeName, searchEmployee)
      })

      // Apply specific position filter if selected
      if (selectedPositionId) {
        filteredRoleEmployees = filteredRoleEmployees.filter(employee =>
          (employee.positions || []).some(
            (pos: IEmployeePosition) =>
              pos.subcategoryId === selectedPositionId ||
              pos.categoryId === selectedPositionId
          )
        )
      }

      // Sort employees by first name within each role
      filteredRoleEmployees.sort((a, b) => {
        const aName = a.name || ''
        const bName = b.name || ''
        return aName.localeCompare(bName)
      })

      filtered[roleId] = {
        role: roleData.role,
        employees: filteredRoleEmployees
      }
    })

    return filtered
  }, [
    employeesByRole,
    searchEmployee,
    roleFilterState,
    selectedPositionId,
    currentCompany.jobs
  ])

  const filterButtonRef = useRef<HTMLButtonElement>(null)
  const drawerRef = useOnclickOutside(
    (e: Event) => {
      if (!filterButtonRef.current?.contains(e.target as Node)) {
        setShowFilterDrawer(false)
      }
    },
    { disabled: !showFilterDrawer }
  )

  const [isEmployeeExpanded, setIsEmployeeExpanded] = useState<{
    [id: string | number]: boolean
  }>({})

  // Calculate current day index - only highlight when viewing current period (offset 0)
  const getCurrentDayIndex = React.useMemo(() => {
    // Only highlight current day when viewing the current period
    if (currentPeriodOffset !== 0) {
      return null // No highlighting for past/future periods
    }

    const today = dayjs().format('YYYY-MM-DD')
    const idx = daysArray.findIndex(day => day.date === today)
    return idx >= 0 ? idx : null
  }, [daysArray, currentPeriodOffset])

  const [activeShiftPopover, setActiveShiftPopover] = useState<string | null>(
    null
  )
  const shiftRefs = useRef<Map<string, HTMLElement | null>>(new Map())

  // Parse active shift popover to get employee, date, and shift info
  const activeShiftInfo = React.useMemo(() => {
    if (!activeShiftPopover) {
      return null
    }
    // Parse shiftId by finding the last two dashes (for dayIndex and shiftIndex)
    // This handles UIDs with multiple internal dashes like '-MCMlGbLjD1rD3fEt-OG'
    const lastDashIndex = activeShiftPopover.lastIndexOf('-')
    const secondLastDashIndex = activeShiftPopover.lastIndexOf(
      '-',
      lastDashIndex - 1
    )
    if (lastDashIndex === -1 || secondLastDashIndex === -1) {
      return null
    }
    const employeeId = activeShiftPopover.substring(0, secondLastDashIndex)
    // Fix: dayIndex should match the index in daysArray, which is the index in the map, not the day of week
    // The index in the shiftId is the index in daysArray, so use it directly
    const dayIndex = Number(
      activeShiftPopover.substring(secondLastDashIndex + 1, lastDashIndex)
    )
    const shiftIndex = activeShiftPopover.substring(lastDashIndex + 1)
    // Find the employee by uid (use filteredEmployees since that's what's displayed)
    const employee = filteredEmployees.find(
      emp => getEmployeeId(emp) === employeeId
    )
    if (!employee) {
      return null
    }
    // Defensive: clamp dayIndex to valid range
    let safeDayIndex = dayIndex
    if (
      isNaN(safeDayIndex) ||
      safeDayIndex < 0 ||
      safeDayIndex >= daysArray.length
    ) {
      return null
    }
    const day = daysArray[safeDayIndex]
    if (!day) {
      return null
    }
    const dayShifts = getEmployeeShiftsForDate(
      getEmployeeId(employee),
      day.date,
      attendanceData
    )
    return {
      employee,
      date: day.date,
      dayIndex: safeDayIndex,
      shiftIndex,
      shifts: dayShifts,
      isNewShift: shiftIndex === 'add'
    }
  }, [activeShiftPopover, filteredEmployees, daysArray, attendanceData])

  /**
   * Determines the overall status for an employee based on all their shifts
   * Returns the highest priority status found across all shifts
   */
  const shouldShowPopoverOnLeftForShift = (shiftId: string | null): boolean => {
    if (!shiftId) return false
    
    const lastDashIndex = shiftId.lastIndexOf('-')
    const secondLastDashIndex = shiftId.lastIndexOf('-', lastDashIndex - 1)
    
    if (lastDashIndex !== -1 && secondLastDashIndex !== -1) {
      const dayIndex = Number(shiftId.substring(secondLastDashIndex + 1, lastDashIndex))
      const clampedDayIndex = Math.max(0, Math.min(dayIndex, payrollLength - 1))
      // Show on left for the last 2 days to prevent overflow
      return clampedDayIndex >= payrollLength - 2
    }
    return false
  }

  // Enhanced popperConfig for robust positioning, especially for Sunday
  // TODO Lie - ?
  // That's should be hanlded with default props from Overlay
  const refinedPopperConfig = React.useMemo(() => {
    let extraOffset = 8
    let forcePlacement: 'left' | 'right' | 'bottom' | undefined = undefined
    if (activeShiftPopover) {
      const lastDashIndex = activeShiftPopover.lastIndexOf('-')
      const secondLastDashIndex = activeShiftPopover.lastIndexOf(
        '-',
        lastDashIndex - 1
      )
      if (lastDashIndex !== -1 && secondLastDashIndex !== -1) {
        // Use the same dayIndex logic as activeShiftInfo
        const dayIndex = Number(
          activeShiftPopover.substring(secondLastDashIndex + 1, lastDashIndex)
        )
        // Defensive: clamp dayIndex to valid range
        let safeDayIndex = dayIndex
        if (
          isNaN(safeDayIndex) ||
          safeDayIndex < 0 ||
          safeDayIndex >= daysArray.length
        ) {
          safeDayIndex = 0
        }
        // Use payrollStartingDay to match popover logic
        let payrollStartingDay = currentCompany?.payrollStartingDay || 'Monday'
        const weekDays = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday'
        ]
        let startIdx = weekDays.findIndex(
          d => d.toLowerCase() === payrollStartingDay.toLowerCase()
        )
        if (startIdx === -1) startIdx = 0
        const columnPosition = (safeDayIndex + startIdx) % 7
        // Rightmost columns (Fri/Sat/Sun): force left, add offset
        if (columnPosition === 6) {
          // Sunday
          extraOffset = 120
          forcePlacement = 'left'
        } else if (columnPosition === 5) {
          // Saturday
          extraOffset = 80
          forcePlacement = 'left'
        } else if (columnPosition === 4) {
          // Friday
          extraOffset = 64
          forcePlacement = 'left'
        } else if (columnPosition === 3) {
          // Thursday
          extraOffset = 48
          forcePlacement = 'left'
        } else if (columnPosition === 2) {
          // Wednesday
          extraOffset = 64
          forcePlacement = 'right'
        } else if (columnPosition === 1) {
          // Tuesday
          extraOffset = 48
          forcePlacement = 'right'
        } else if (columnPosition === 0) {
          // Monday
          extraOffset = 64
          forcePlacement = 'right'
        }
      }
    }
    return {
      placement: forcePlacement,
      modifiers: [
        {
          name: 'preventOverflow',
          options: {
            boundary: 'viewport',
            padding: 10,
            mainAxis: true,
            altAxis: true,
            tether: false
          }
        },
        {
          name: 'flip',
          options: {
            fallbackPlacements: ['bottom', 'left', 'right', 'top']
          }
        },
        {
          name: 'offset',
          options: {
            offset: [0, extraOffset]
          }
        },
        {
          name: 'customMaxRight',
          enabled: true,
          phase: 'main' as const,
          fn({ state }: { state: Record<string, unknown> }) {
            const stateTyped = state as {
              rects: { popper: { width: number; height: number } }
              placement: string
              styles: { popper: Record<string, string> }
            }
            const { popper } = stateTyped.rects
            const { placement, styles } = stateTyped
            const popperStyles = styles.popper
            const viewportWidth = window.innerWidth

            if (
              !(
                placement.startsWith('left') || placement.startsWith('right')
              ) ||
              !popperStyles
            )
              return

            const left = parseFloat(popperStyles.left ?? '0')

            // Clamp left so popover never overflows right edge
            if (left + popper.width > viewportWidth - 8) {
              popperStyles.left = `${viewportWidth - popper.width - 8}px`
            }
            // Clamp left to minimum 8px from left edge
            if (left < 8) {
              popperStyles.left = '8px'
            }
            // If popover is still too wide, force width to fit viewport
            if (popper.width > viewportWidth - 16) {
              popperStyles.width = `${viewportWidth - 16}px`
            }
          }
        }
      ]
    }
  }, [activeShiftPopover, daysArray.length, currentCompany?.payrollStartingDay])

  return (
    <>
      <FilterDrawer
        show={showFilterDrawer}
        drawerRef={drawerRef}
        departmentRoles={departmentRoles}
        filterState={roleFilterState}
        onFilterChange={onRoleFilterChange}
        isSingleRole={isSingleRole}
        singleRoleInfo={singleRoleInfo}
        onSwitchToRoles={() => setDisplayBy('role')}
        currentDisplayBy={displayBy}
      />

      <TableBlockStyled>
        <TableHeaderStyled>
          <OptionBlockStyled>
            <TotalEmployeesStyled>
              {filteredEmployees.length} {I18n.t('common.employees')}
            </TotalEmployeesStyled>
            <InputWrapStyled>
              <InputStyled
                type='search'
                name='Employee search'
                placeholder={I18n.t('employees.searchEmployee') + '...'}
                onChange={e => onSearchEmployee(e.target.value)}
                value={searchEmployee}
              />
              <SearchIconStyled />
            </InputWrapStyled>
            <WeekPeriodTabsStyled role='tablist'>
              {displayByArray.map(tab => (
                <TabButtonStyled
                  key={tab.id}
                  role='tab'
                  aria-selected={displayBy === tab.id}
                  onClick={() => setDisplayBy(tab.id)}
                  $isActive={displayBy === tab.id}
                >
                  {tab.icon}
                  {tab.label}
                </TabButtonStyled>
              ))}
            </WeekPeriodTabsStyled>
            <FilterButtonStyled
              ref={filterButtonRef}
              onClick={() => {
                if (displayBy === 'role') {
                  setShowFilterDrawer(!showFilterDrawer)
                } else {
                  // Switch to "Roles" view AND open FilterDrawer when clicking filter in "Employees" view
                  setDisplayBy('role')
                  setShowFilterDrawer(true)
                }
              }}
            >
              <FilterIconStyled />
              {I18n.t('payroll.filter')}
            </FilterButtonStyled>
          </OptionBlockStyled>
          <SummaryBlockStyled>
            <TotalHourStyled>
              <ClockIconStyled />
              <span>{totalHours === 0 ? 0 : totalHours.toFixed(1)}</span>{' '}
              {I18n.t('payroll.total_hours')}
            </TotalHourStyled>
            <TotalSalaryStyled>
              <span>
                <NumberFormatted value={totalSalary} />
              </span>
              {I18n.t('payroll.salaries')}
            </TotalSalaryStyled>
          </SummaryBlockStyled>
        </TableHeaderStyled>

        <TableStyled>
          {displayBy === 'role' ? (
            <>
              {Object.entries(filteredEmployeesByRole)
                .sort(([roleIdA, roleDataA], [roleIdB, roleDataB]) => {
                  // Sort by role priority (same as RolesList.js logic)
                  const priorityA = roleDataA.role.priority || 0
                  const priorityB = roleDataB.role.priority || 0
                  return priorityA - priorityB
                })
                .map(([roleId, roleData]) => (
                  <TableRowStyled key={roleId}>
                    <HeaderStyled
                      onClick={() =>
                        setExpandedRows(prev => ({
                          ...prev,
                          [roleId]: !prev[roleId]
                        }))
                      }
                    >
                      <HeaderTitleStyled>
                        {roleData.role.name}{' '}
                        <span>{roleData.employees.length}</span>
                      </HeaderTitleStyled>
                      <ArrowDownIconStyled
                        src={arrowDownIcon}
                        alt=''
                        $isExpanded={expandedRows[roleId]}
                      />
                    </HeaderStyled>
                    <BodyStyled $isExpanded={expandedRows[roleId]}>
                      {roleData.employees.map((employee: IEmployee, employeeIndex: number) => {
                        // Use PayrollOld logic for employee number display
                        const employeeNumber = employee.payrollId || employee.customId || addZeros(employeeIndex + 1)

                        // Remove employee-level border colors - colors should only appear on individual shift cards
                        // const employeeStatus = getEmployeeOverallStatus(employee)

                        return (
                          <ItemStyled
                            key={employee.uid}
                          >
                            <ItemHeaderStyled
                              role='button'
                              onClick={() =>
                                setIsEmployeeExpanded(prev => ({
                                  ...prev,
                                  [getEmployeeId(employee)]:
                                    !prev[getEmployeeId(employee)]
                                }))
                              }
                            >
                              <EmployeeInfoStyled>
                                <AvatarInitialsStyled employee={employee} />
                                <EmployeeInfoColumnStyled>
                                  <EmployeeNameStyled>
                                    {employee.name} {employee.surname}
                                  </EmployeeNameStyled>
                                  <EmployeeIdStyled>
                                    {employeeNumber}
                                  </EmployeeIdStyled>
                                </EmployeeInfoColumnStyled>
                              </EmployeeInfoStyled>

                              <EmployeeStatsStyled>
                                {(() => {
                                  const { weeklyStats } =
                                    getEmployeeWeeklyStats(
                                      getEmployeeId(employee),
                                      attendanceData,
                                      startOfPeriod,
                                      payrollLength
                                    )
                                  return weeklyStats.map((week, weekIndex) => (
                                    <EmployeeStatsBlockStyled key={weekIndex}>
                                      <EmployeeStatsBlockItemStyled>
                                        {payrollLength === 14
                                          ? `${I18n.t('common.week_shorten')}${weekIndex + 1}`
                                          : ''}
                                        <ClockIconStyled />{' '}
                                        {week.hours.toFixed(1)}
                                        {I18n.t(
                                          'common.hours_shorten'
                                        ).toLowerCase()}
                                      </EmployeeStatsBlockItemStyled>
                                      <EmployeeStatsBlockItemStyled>
                                        <NumberFormatted value={week.salary} />
                                      </EmployeeStatsBlockItemStyled>
                                    </EmployeeStatsBlockStyled>
                                  ))
                                })()}
                              </EmployeeStatsStyled>
                            </ItemHeaderStyled>
                            {!!isEmployeeExpanded[getEmployeeId(employee)] && (
                              <ItemBodyStyled $isWeekly={false}>
                                {daysArray.map((day, index) => (
                                  <DayItemStyled
                                    key={index}
                                    $isToday={index === getCurrentDayIndex}
                                    $hasShifts={(() => {
                                      const dayShifts = getEmployeeShiftsForDate(
                                        getEmployeeId(employee),
                                        day.date,
                                        attendanceData
                                      )
                                      return dayShifts.length > 0
                                    })()}
                                  >
                                    <DayItemHeaderStyled>
                                      <DayItemTitleStyled>
                                        {day.dayName} {day.dayNumber}
                                      </DayItemTitleStyled>
                                      <AddShiftButtonStyled
                                        ref={el => {
                                          const addShiftId = `${getEmployeeId(employee)}-${index}-add`
                                          shiftRefs.current.set(addShiftId, el)
                                        }}
                                        onClick={() => {
                                          const shiftId = `${getEmployeeId(employee)}-${index}-add`
                                          setActiveShiftPopover(shiftId)
                                        }}
                                      >
                                        <PlusIconStyled />
                                      </AddShiftButtonStyled>
                                    </DayItemHeaderStyled>
                                    {(() => {
                                      const dayShifts =
                                        getEmployeeShiftsForDate(
                                          getEmployeeId(employee),
                                          day.date,
                                          attendanceData
                                        )
                                      if (dayShifts.length === 0) {
                                        return null
                                      }
                                      return (
                                        <DayItemBodyStyled>
                                          <DayItemBodyLabelBlockStyled>
                                            <LabelBlockHeadStyled>
                                              <LabelStyled>
                                                {I18n.t('payroll.start')}
                                              </LabelStyled>
                                              <LabelStyled>
                                                {I18n.t('common.end')}
                                              </LabelStyled>
                                            </LabelBlockHeadStyled>
                                            <LabelStyled>
                                              {I18n.t('payroll.break')}
                                            </LabelStyled>
                                          </DayItemBodyLabelBlockStyled>
                                          {dayShifts.map(
                                            (
                                              shift: AttendanceShift & {
                                                isClockInDifferent?: boolean
                                                isClockOutDiffrent?: boolean
                                              },
                                              shiftIndex: number
                                            ) => {
                                              const shiftId = `${getEmployeeId(employee)}-${index}-${shiftIndex}`
                                              
                                              return renderShiftItem(
                                                shift,
                                                shiftId,
                                                getEmployeeId(employee),
                                                day.date,
                                                day.isToday,
                                                attendanceData,
                                                currentCompany,
                                                shiftRefs,
                                                setActiveShiftPopover
                                              )
                                            }
                                          )}
                                        </DayItemBodyStyled>
                                      )
                                    })()}
                                  </DayItemStyled>
                                ))}
                              </ItemBodyStyled>
                            )}
                          </ItemStyled>
                        )
                      })}
                    </BodyStyled>
                  </TableRowStyled>
                )
              )}
            </>
          ) : (
            <TableRowStyled>
              <BodyStyled
                $isExpanded={true}
                $noBorder
              >
                {filteredEmployees.map((employee, employeeIndex) => {
                  // Remove employee-level border colors - colors should only appear on individual shift cards
                  // const employeeStatus = getEmployeeOverallStatus(employee)

                  return (
                    <ItemStyled
                      key={employee.id}
                    >
                    <ItemHeaderStyled
                      role='button'
                      onClick={() =>
                        setIsEmployeeExpanded(prev => ({
                          ...prev,
                          [getEmployeeId(employee)]:
                            !prev[getEmployeeId(employee)]
                        }))
                      }
                    >
                      <EmployeeInfoStyled>
                        <AvatarInitialsStyled
                          employee={employee as unknown as IEmployee}
                        />
                        <EmployeeInfoColumnStyled>
                          <EmployeeNameStyled>
                            {employee.name} {employee.surname}
                          </EmployeeNameStyled>
                          <EmployeeIdStyled>
                            {employee.payrollId || employee.customId || addZeros(employeeIndex + 1)}
                          </EmployeeIdStyled>
                        </EmployeeInfoColumnStyled>
                      </EmployeeInfoStyled>

                      <EmployeeStatsStyled>
                        {(() => {
                          const { weeklyStats } = getEmployeeWeeklyStats(
                            employee.uid || '',
                            attendanceData,
                            startOfPeriod,
                            payrollLength
                          )
                          return weeklyStats.map((week, weekIndex) => (
                            <EmployeeStatsBlockStyled key={weekIndex}>
                              <EmployeeStatsBlockItemStyled>
                                {payrollLength === 14
                                  ? `${I18n.t('common.week_shorten')}${weekIndex + 1}`
                                  : ''}
                                <ClockIconStyled /> {week.hours.toFixed(1)}
                                {I18n.t('common.hours_shorten').toLowerCase()}
                              </EmployeeStatsBlockItemStyled>
                              <EmployeeStatsBlockItemStyled>
                                $ {week.salary.toFixed(2)}
                              </EmployeeStatsBlockItemStyled>
                            </EmployeeStatsBlockStyled>
                          ))
                        })()}
                      </EmployeeStatsStyled>
                    </ItemHeaderStyled>
                    {isEmployeeExpanded[getEmployeeId(employee)] && (
                      <ItemBodyStyled $isWeekly={false}>
                        {daysArray.map((day, index) => {
                          // Calculate day-level status using helper function
                          const dayShifts = getEmployeeShiftsForDate(
                            getEmployeeId(employee),
                            day.date,
                            attendanceData
                          )

                          const { dayStatusColor } = getDayStatusAndColor(
                            dayShifts,
                            getEmployeeId(employee),
                            day.date,
                            attendanceData,
                            currentCompany,
                            day.isToday
                          )

                          return (
                            <DayItemStyled
                              key={index}
                              $isToday={index === getCurrentDayIndex}
                              $isBlue={dayStatusColor === 'blue'}
                              $isGrey={dayStatusColor === 'grey'}
                              $isOrange={dayStatusColor === 'orange'}
                              $isRed={dayStatusColor === 'red'}
                              $hasShifts={dayShifts.length > 0}
                            >
                            <DayItemHeaderStyled>
                              <DayItemTitleStyled>
                                {day.dayName} {day.dayNumber}
                              </DayItemTitleStyled>
                              <AddShiftButtonStyled
                                ref={el => {
                                  const addShiftId = `${getEmployeeId(employee)}-${index}-add`
                                  shiftRefs.current.set(addShiftId, el)
                                }}
                                onClick={() => {
                                  const shiftId = `${getEmployeeId(employee)}-${index}-add`
                                  setActiveShiftPopover(shiftId)
                                }}
                              >
                                <PlusIconStyled />
                              </AddShiftButtonStyled>
                            </DayItemHeaderStyled>
                            {(() => {
                              const dayShifts = getEmployeeShiftsForDate(
                                getEmployeeId(employee),
                                day.date,
                                attendanceData
                              )
                              if (dayShifts.length === 0) {
                                return null
                              }
                              return (
                                <DayItemBodyStyled>
                                  <DayItemBodyLabelBlockStyled>
                                    <LabelBlockHeadStyled>
                                      <LabelStyled>
                                        {I18n.t('payroll.start')}
                                      </LabelStyled>
                                      <LabelStyled>
                                        {I18n.t('common.end')}
                                      </LabelStyled>
                                    </LabelBlockHeadStyled>
                                    <LabelStyled>
                                      {I18n.t('payroll.break')}
                                    </LabelStyled>
                                  </DayItemBodyLabelBlockStyled>
                                  {dayShifts.map(
                                    (
                                      shift: AttendanceShift & {
                                        isClockInDifferent?: boolean
                                        isClockOutDiffrent?: boolean
                                      },
                                      shiftIndex: number
                                    ) => {
                                      const shiftId = `${getEmployeeId(employee)}-${index}-${shiftIndex}`
                                      
                                      return renderShiftItem(
                                        shift,
                                        shiftId,
                                        getEmployeeId(employee),
                                        day.date,
                                        day.isToday,
                                        attendanceData,
                                        currentCompany,
                                        shiftRefs,
                                        setActiveShiftPopover
                                      )
                                    }
                                  )}
                                </DayItemBodyStyled>
                              )
                            })()}
                          </DayItemStyled>
                          )
                        })}
                      </ItemBodyStyled>
                    )}
                  </ItemStyled>
                  )
                })}
              </BodyStyled>
            </TableRowStyled>
          )}
        </TableStyled>
      </TableBlockStyled>
      <Overlay
        rootClose
        show={activeShiftPopover !== null}
        target={() =>
          activeShiftPopover
            ? (shiftRefs.current.get(activeShiftPopover) ?? null)
            : null
        }
        placement={
          shouldShowPopoverOnLeftForShift(activeShiftPopover) ? 'left' : 'right'
        }
        onHide={() => {
          const activitiesPopover = document.getElementById(
            'payroll_activities-popover'
          )
          if (activitiesPopover) {
            return
          }
          setActiveShiftPopover(null)
        }}
        // TODO Lie - ?
        container={undefined}
        // TODO Lie - ?
        flip={true}
        popperConfig={refinedPopperConfig}
      >
        {activeShiftInfo ? (
          <ShiftPopover
            onClose={() => setActiveShiftPopover(null)}
            showActivityPopoverOnLeft={shouldShowPopoverOnLeftForShift(
              activeShiftPopover
            )}
            employee={activeShiftInfo.employee as unknown as IEmployee}
            date={activeShiftInfo.date}
            shifts={activeShiftInfo.shifts || []}
            isNewShift={activeShiftInfo.isNewShift || false}
            currentCompany={currentCompany}
            attendanceData={attendanceData}
            onSave={(newShift: { [key: string]: AttendanceShift }) =>
              onSave(
                newShift,
                activeShiftInfo.employee.uid,
                activeShiftInfo.date
              )
            }
            onDeleteShift={(shiftKey: string) =>
              onDeleteShift(
                shiftKey,
                activeShiftInfo.employee.uid,
                activeShiftInfo.date
              )
            }
          />
        ) : (
          <div
            style={{
              padding: '1rem',
              textAlign: 'center',
              backgroundColor: 'white',
              border: '1px solid #ccc',
              borderRadius: '8px'
            }}
          >
            Loading shift details...
          </div>
        )}
      </Overlay>
    </>
  )
}

export default HoursTable
