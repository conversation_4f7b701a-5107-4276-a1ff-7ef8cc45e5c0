import React, { useState } from 'react'
import ConflictShiftModal from './ConflictShiftModal'

// Demo component to test ConflictShiftModal functionality
const ConflictShiftModalDemo: React.FC = () => {
  const [showModal, setShowModal] = useState(false)

  // Sample conflict data for testing
  const sampleConflicts = [
    {
      id: 'conflict-1',
      employeeId: 'emp-1',
      employeeName: 'Alek Riendeau',
      date: '2024-01-15',
      type: 'clocked-in-early' as const,
      shiftData: {
        scheduledStart: '10:00',
        actualStart: '09:30',
        scheduledEnd: '18:00',
        actualEnd: '18:00',
        role: 'Waiter'
      }
    },
    {
      id: 'conflict-2',
      employeeId: 'emp-2',
      employeeName: '<PERSON>',
      date: '2024-01-15',
      type: 'missing-end' as const,
      shiftData: {
        scheduledStart: '12:00',
        actualStart: '12:00',
        scheduledEnd: '20:00',
        actualEnd: null,
        role: 'Cook'
      }
    },
    {
      id: 'conflict-3',
      employeeId: 'emp-3',
      employeeName: 'Alphon<PERSON>',
      date: '2024-01-15',
      type: 'clocked-out-late' as const,
      shiftData: {
        scheduledStart: '14:00',
        actualStart: '14:00',
        scheduledEnd: '22:00',
        actualEnd: '22:45',
        role: 'Server'
      }
    },
    {
      id: 'conflict-4',
      employeeId: 'emp-4',
      employeeName: 'Amina Goudjil',
      date: '2024-01-16',
      type: 'unplanned-shift' as const,
      shiftData: {
        scheduledStart: null,
        actualStart: '16:00',
        scheduledEnd: null,
        actualEnd: '20:00',
        role: null
      }
    },
    {
      id: 'conflict-5',
      employeeId: 'emp-5',
      employeeName: 'Andrei Moldovan',
      date: '2024-01-16',
      type: 'shift-too-short' as const,
      shiftData: {
        scheduledStart: '18:00',
        actualStart: '18:00',
        scheduledEnd: '18:30',
        actualEnd: '18:05',
        role: 'Dishwasher'
      }
    },
    {
      id: 'conflict-6',
      employeeId: 'emp-1',
      employeeName: 'Alek Riendeau',
      date: '2024-01-17',
      type: 'shift-under-3-hours' as const,
      shiftData: {
        scheduledStart: '10:00',
        actualStart: '10:00',
        scheduledEnd: '12:30',
        actualEnd: '12:30',
        role: 'Waiter'
      }
    }
  ]

  const handleResolveConflict = (conflictId: string, resolution: any) => {
    console.log('Demo: Resolving conflict', conflictId, resolution)
    // In a real implementation, this would update the backend and remove the conflict
  }

  const attendanceSettings = {
    roundingTime: 15,
    overtimeCalculationMode: 'weekly',
    payrollFrequency: 'biweekly'
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>ConflictShiftModal Demo</h2>
      <p>This demo shows the new ConflictShiftModal with all 6 conflict types.</p>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Features Demonstrated:</h3>
        <ul>
          <li>✅ 6 conflict types with priority ordering</li>
          <li>✅ View options (Entire Period, Week 1, Week 2, Today)</li>
          <li>✅ Conflict count indicators</li>
          <li>✅ Auto-scroll to selected conflict type</li>
          <li>✅ Scroll-based active type highlighting</li>
          <li>✅ Conflict resolution with notifications</li>
          <li>✅ Undo functionality</li>
          <li>✅ "You saved" amount tracking</li>
          <li>✅ Reclaim animation on close</li>
          <li>✅ "All caught up" state</li>
        </ul>
      </div>

      <button 
        onClick={() => setShowModal(true)}
        style={{
          padding: '10px 20px',
          backgroundColor: '#FF4D00',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          cursor: 'pointer',
          fontSize: '14px'
        }}
      >
        Open Conflict Modal ({sampleConflicts.length} conflicts)
      </button>

      <ConflictShiftModal
        show={showModal}
        onHide={() => setShowModal(false)}
        conflicts={sampleConflicts}
        onResolveConflict={handleResolveConflict}
        attendanceSettings={attendanceSettings}
      />
    </div>
  )
}

export default ConflictShiftModalDemo
