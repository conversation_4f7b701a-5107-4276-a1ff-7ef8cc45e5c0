import React, { useState } from 'react'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import { I18n } from 'react-redux-i18n'
import { toast } from 'react-toastify'

import { Moment } from 'moment'

import NumberFormatted from 'components/ui/NumberFormatted'

import DeleteShiftModal from '../../DeleteShiftModal'
import PayrollToaster from '../../PayrollToaster'

import checkIcon from 'img/icons/checkBlueIcon.svg'

import {
  CardStyled,
  CardHeaderStyled,
  CardTitleStyled,
  CardSubtitleStyled,
  CardListStyled,
  CardItemStyled,
  DateStyled,
  WarningIconStyled,
  InfoBlockStyled,
  AvatarInitialsStyled,
  InfoBlockTextStyled,
  RoleBlockStyled,
  RoleBlockLabelStyled,
  CustomSelectStyled,
  ShiftBlockStyled,
  RowCellStyled,
  RowStyled,
  CustomTimePickerStyled,
  CardFooterStyled,
  ButtonWrapStyled,
  FooterButtonStyled,
  DeleteButtonStyled,
  DeleteIconStyled,
  OrangeButtonStyled,
  GreyButtonStyled,
  GreenButtonStyled,
  CardEmptyBlockStyled,
  TooltipStyled
} from '../../../../styles/ShiftCard.styles'

const CONFLICT_TYPE_CONFIG = {
  'clocked-in-early': {
    title: I18n.t('payroll.clocked_in_early'),
    subtitle: I18n.t('payroll.the_employee_clocked_in_earlier_than_planned')
  },
  'missing-end': {
    title: I18n.t('payroll.missing_end'),
    subtitle: I18n.t('payroll.the_employee_did_not_clock_out_of_their_shift')
  },
  'clocked-out-late': {
    title: I18n.t('payroll.clocked_out_late'),
    subtitle: I18n.t('payroll.the_employee_clocked_out_later_than_planned')
  },
  'unplanned-shift': {
    title: I18n.t('payroll.unplanned_shift'),
    subtitle: I18n.t(
      'payroll.the_clocked_shift_was_not_planned_for_this_employee'
    )
  },
  'shift-too-short': {
    title: I18n.t('payroll.shift_too_short'),
    subtitle: I18n.t('payroll.shift_was_shorter_than_expected')
  },
  'shift-under-3-hours': {
    title: I18n.t('payroll.shift_under_3_hours'),
    subtitle: I18n.t('payroll.shift_was_under_3_hours')
  }
} as const

interface ConflictShift {
  id: string
  employeeId: string
  employeeName: string
  date: string
  type: string
  shiftData: any
  resolved?: boolean
}

interface ShiftCardProps {
  activeType: string
  conflicts?: ConflictShift[]
  conflictItems?: string[] // Keep for backward compatibility
  onResolveConflict?: (conflictId: string, resolution: { type: 'approve' | 'delete' | 'claim' | 'modify', savedAmount?: number }) => void
  onUndoResolve?: (conflictId: string, savedAmount?: number) => void
  attendanceSettings?: any
}

const ShiftCard: React.FC<ShiftCardProps> = ({
  activeType,
  conflicts = [],
  conflictItems = [],
  onResolveConflict,
  onUndoResolve,
  attendanceSettings
}) => {
  const options = [
    {
      label: 'Waiter',
      value: 'waiter'
    },
    {
      label: 'Cook',
      value: 'cook'
    }
  ]
  const [selectedRole, setSelectedRole] = useState<string | null>(null)
  const [shiftTimeStart, setShiftTimeStart] = useState<Moment | null>(null)
  const [shiftTimeEnd, setShiftTimeEnd] = useState<Moment | null>(null)
  const [hasDecision, setHasDecision] = useState(false)
  const [hasClaimed, setHasClaimed] = useState(false)
  const { title, subtitle } =
    CONFLICT_TYPE_CONFIG[activeType as keyof typeof CONFLICT_TYPE_CONFIG]

  const hidePlannedRow =
    activeType === 'unplanned-shift' || activeType === 'missing-end'

  const isUnplannedShift = activeType === 'unplanned-shift'
  const canReclaim = activeType === 'clocked-in-early'

  const [showDeleteShiftModal, setShowDeleteShiftModal] = useState(false)

  const saveAmount = 5.42

  const onChangeStatus = (
    conflictId: string,
    status: 'approve' | 'delete' | 'claim' | 'modify',
    savedAmount?: number
  ) => {
    setHasDecision(true)

    // Call parent resolve handler
    if (onResolveConflict) {
      onResolveConflict(conflictId, { type: status, savedAmount })
    }

    // Show toast notification with undo functionality
    toast(
      <PayrollToaster
        onUndo={() => {
          if (onUndoResolve) {
            onUndoResolve(conflictId, savedAmount)
          }
          setHasDecision(false)
          setHasClaimed(false)
        }}
        status={status}
      />,
      {
        className: 'payroll-toaster',
        closeButton: false,
        hideProgressBar: true,
        position: 'top-right',
        autoClose: 5000,
        pauseOnFocusLoss: false
      }
    )
  }

  // Use conflicts if provided, otherwise fall back to conflictItems for backward compatibility
  const displayItems = conflicts.length > 0 ? conflicts : conflictItems.map(item => ({
    id: item,
    employeeId: item,
    employeeName: 'John Doe', // TODO: Get from actual data
    date: 'Friday 10',
    type: activeType,
    shiftData: {}
  }))

  return (
    <CardStyled $isEmpty={displayItems.length === 0}>
      <CardHeaderStyled>
        <CardTitleStyled>{title}</CardTitleStyled>
        <CardSubtitleStyled>{subtitle}</CardSubtitleStyled>
      </CardHeaderStyled>

      {displayItems.length > 0 ? (
        <CardListStyled>
          {displayItems.map(item => (
            <CardItemStyled
              key={item.id}
              $hasOverlay={hasDecision}
              $isDeleting={showDeleteShiftModal}
            >
              {showDeleteShiftModal && (
                <DeleteShiftModal
                  onClose={() => setShowDeleteShiftModal(false)}
                  onDelete={() => {
                    onChangeStatus(item.id, 'delete')
                    setShowDeleteShiftModal(false)
                  }}
                  isConflictModal
                />
              )}
              <DateStyled>
                {item.date}
                <WarningIconStyled />
              </DateStyled>
              <InfoBlockStyled>
                <AvatarInitialsStyled employee={undefined} />
                <InfoBlockTextStyled>{item.employeeName}</InfoBlockTextStyled>
              </InfoBlockStyled>
              <RoleBlockStyled>
                <RoleBlockLabelStyled>
                  {I18n.t('common.role')}
                </RoleBlockLabelStyled>
                <CustomSelectStyled
                  options={options}
                  value={options.find(option => option.value === selectedRole)}
                  onChange={option =>
                    option && setSelectedRole(option.value as string)
                  }
                  placeholder={I18n.t('common.select')}
                  noOptionsMessage={I18n.t('payroll.no_roles_available')}
                  components={{
                    IndicatorSeparator: null
                  }}
                  $noValue={!selectedRole}
                />
              </RoleBlockStyled>
              <ShiftBlockStyled>
                <RowStyled>
                  <RowCellStyled />
                  <RowCellStyled>{I18n.t('payroll.start')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('common.end')}</RowCellStyled>
                </RowStyled>
                {!hidePlannedRow && (
                  <RowStyled>
                    <RowCellStyled>{I18n.t('payroll.planned')}</RowCellStyled>
                    <RowCellStyled>10:00</RowCellStyled>
                    <RowCellStyled>18:00</RowCellStyled>
                  </RowStyled>
                )}
                <RowStyled>
                  <RowCellStyled>{I18n.t('common.shift')}</RowCellStyled>
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setShiftTimeStart(value)
                    }}
                    value={shiftTimeStart}
                    placeholder='-'
                    hideArrow
                    $hasConflict={true}
                  />
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setShiftTimeEnd(value)
                    }}
                    value={shiftTimeEnd}
                    placeholder='-'
                    hideArrow
                    $hasConflict={false}
                  />
                </RowStyled>
              </ShiftBlockStyled>

              <CardFooterStyled>
                <ButtonWrapStyled>
                  {isUnplannedShift && (
                    <DeleteButtonStyled
                      onClick={() => setShowDeleteShiftModal(true)}
                    >
                      <DeleteIconStyled />
                      {I18n.t('common.delete')}
                    </DeleteButtonStyled>
                  )}

                  {canReclaim && (
                    <OverlayTrigger
                      trigger={['hover', 'focus']}
                      placement='bottom-start'
                      overlay={
                        <TooltipStyled id='conflict-shift-modal_reclaim-tooltip'>
                          {I18n.t('payroll.you_save')}{' '}
                          <NumberFormatted value={saveAmount} />{' '}
                          {I18n.t('payroll.with_planned_start_time')}
                        </TooltipStyled>
                      }
                    >
                      <OrangeButtonStyled
                        color='orange'
                        onClick={() => {
                          onChangeStatus(item.id, 'claim', saveAmount)
                          setHasClaimed(!hasClaimed)
                        }}
                        $isActive={hasClaimed}
                      >
                        <p>
                          + <NumberFormatted value={saveAmount} />
                        </p>
                        <p>
                          {I18n.t('payroll.saved')}{' '}
                          <NumberFormatted value={saveAmount} />
                        </p>
                      </OrangeButtonStyled>
                    </OverlayTrigger>
                  )}
                </ButtonWrapStyled>

                <ButtonWrapStyled>
                  {isUnplannedShift ? (
                    <GreyButtonStyled
                      onClick={() => onChangeStatus(item.id, 'modify')}
                    >
                      {I18n.t('common.save')}
                    </GreyButtonStyled>
                  ) : (
                    <GreenButtonStyled
                      color='green'
                      onClick={() => {
                        if (hasDecision) {
                          setHasDecision(false)
                          setHasClaimed(false)
                        } else {
                          onChangeStatus(item.id, 'approve')
                        }
                      }}
                      $isActive={hasDecision}
                    >
                      <span>{I18n.t('common.approve')}</span>
                      <span>
                        <NumberFormatted value={saveAmount} />
                      </span>
                    </GreenButtonStyled>
                  )}
                </ButtonWrapStyled>
              </CardFooterStyled>
            </CardItemStyled>
          ))}
        </CardListStyled>
      ) : (
        <CardEmptyBlockStyled>
          <img
            src={checkIcon}
            alt=''
          />
          {I18n.t('payroll.no_conflicts')}
        </CardEmptyBlockStyled>
      )}
    </CardStyled>
  )
}

export default ShiftCard
