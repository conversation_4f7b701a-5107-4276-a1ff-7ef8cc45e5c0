import React, { forwardRef, useEffect, useMemo, useRef, useState } from 'react'
import Overlay from 'react-bootstrap/Overlay'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'
import { toast } from 'react-toastify'

import dayjs from 'dayjs'
import moment, { Moment } from 'moment'
import { RootState } from 'store/reducers'

import ActivitiesPopover from './ActivitesPopover'
import DeleteShiftModal from './DeleteShiftModal'
import PayrollToaster from './PayrollToaster'
import { useShiftConflict } from '../../contexts/ShiftConflictContext'

import {
  PopoverStyled,
  ContainerStyled,
  HeaderStyled,
  HeaderTitleStyled,
  ActivityButtonStyled,
  SearchIconStyled,
  CloseButtonStyled,
  InfoBlockStyled,
  AvatarInitialsStyled,
  InfoBlockTextStyled,
  ShiftListStyled,
  ShiftListItemStyled,
  PlusIconStyled,
  RoleBlockStyled,
  RoleBlockLabelStyled,
  Role<PERSON>lockSalaryStyled,
  CustomSelectStyled,
  ShiftBlockStyled,
  RowCellStyled,
  RowStyled,
  CustomTimePickerStyled,
  DeleteButtonStyled,
  DeleteIconStyled,
  BreakBlockStyled,
  DividerStyled,
  CustomCheckboxStyled,
  AddBreakButtonStyled,
  ScrollBlockStyled,
  ButtonBlockStyled,
  SaveButtonStyled,
  OrangeButtonStyled,
  GreyButtonStyled,
  TotalBlockStyled,
  TotalBlockLabelStyled,
  TotalBlockValueStyled,
  WarningButtonStyled,
  WarningIconStyled,
} from '../../styles/ShiftPopover.styles'

import { AttendanceShift } from 'types/attendance'
import { Company } from 'types/company'
import { IEmployee } from 'types/employee'

import { getEmployeeRate } from 'utils/employees'
import { getShiftStatusColor } from 'utils/payroll/shiftStatusAnalysis'

import closeIcon from 'img/icons/closeIcon.svg'

// Types
interface ExtendedAttendanceShift extends AttendanceShift {
  shiftKey?: string
  scheduledShift?: {
    start: number
    end: number
  }
}

interface ShiftPopoverProps {
  onClose: () => void
  showActivityPopoverOnLeft?: boolean
  employee: IEmployee
  date: string
  shifts?: AttendanceShift[]
  isNewShift?: boolean
  currentCompany: Company
  attendanceData: any
  onSave: (shifts: { [key: string]: AttendanceShift }) => Promise<void>
  onDeleteShift?: (shiftKey: string) => Promise<void>
}

const ShiftPopoverSimplified = forwardRef<HTMLDivElement, ShiftPopoverProps>(
  (
    {
      onClose,
      showActivityPopoverOnLeft,
      employee,
      date,
      shifts = [],
      isNewShift = false,
      currentCompany,
      attendanceData,
      onSave,
      onDeleteShift,
      ...rest
    },
    ref
  ) => {
    // Use the context for conflict analysis
    const { getShiftAnalysis, analyzeShift, analyzeAllShifts } = useShiftConflict()

    const [activeShift, setActiveShift] = useState<number | null>(0)
    const [unsavedChanges, setUnsavedChanges] = useState<{[shiftIndex: number]: Partial<AttendanceShift>}>({})
    const [waitingForNewShift, setWaitingForNewShift] = useState<number | null>(null)
    const [selectedRole, setSelectedRole] = useState<string | null>(null)
    const [roundedTimeStart, setRoundedTimeStart] = useState<Moment | null>(null)
    const [roundedTimeEnd, setRoundedTimeEnd] = useState<Moment | null>(null)
    
    // Tooltip states
    const [showPlannedTooltip, setShowPlannedTooltip] = useState(false)
    const [showClockedTooltip, setShowClockedTooltip] = useState(false)
    const [showOverlapTooltip, setShowOverlapTooltip] = useState(false)
    const [showRoleTooltip, setShowRoleTooltip] = useState(false)
    const [canShowTooltip, setCanShowTooltip] = useState(false)
    const [showDeleteBreakTooltip, setShowDeleteBreakTooltip] = useState<string | null>(null)
    const [showActivityPopover, setShowActivityPopover] = useState(false)
    const [showDeleteShiftModal, setShowDeleteShiftModal] = useState(false)

    const isLocaleFr = useSelector((state: RootState) => state.i18n.locale) === 'fr'

    // Generate position options
    const options = React.useMemo(() => {
      if (!currentCompany?.jobs || !employee?.positions) {
        return []
      }

      const positionOptions: Array<{ label: string; value: string }> = []
      const jobsCopy = { ...currentCompany.jobs }
      const sortedJobs = Object.entries(jobsCopy)
        .filter(([_, value]) => value && !value.archived)
        .map(([key, value]) => ({ ...value, key }))
        .sort((a, b) => a.priority - b.priority)

      sortedJobs.forEach((category) => {
        const hasCategory = employee.positions?.some(p => p.categoryId === category.key) || false
        if (hasCategory) {
          positionOptions.push({
            label: category.name,
            value: category.key
          })
        }
      })

      return positionOptions
    }, [currentCompany?.jobs, employee?.positions])

    // Clear unsaved changes when shifts prop changes
    useEffect(() => {
      setUnsavedChanges({})
    }, [shifts])

    // Watch for new shifts appearing after creation
    useEffect(() => {
      if (waitingForNewShift !== null && shifts.length > waitingForNewShift) {
        setActiveShift(waitingForNewShift)
        setWaitingForNewShift(null)
      }
    }, [shifts.length, waitingForNewShift])

    // Analyze all shifts when they change
    useEffect(() => {
      if (shifts.length > 0 && employee && date) {
        analyzeAllShifts(shifts, employee.uid, date)
      }
    }, [shifts, employee, date, analyzeAllShifts])

    // Helper function to update local shift data
    const updateLocalShift = React.useCallback((shiftIndex: number, updates: Partial<AttendanceShift>) => {
      setUnsavedChanges(prev => {
        const currentUnsaved = prev[shiftIndex] || {}
        let hasChanges = false
        
        for (const [key, newValue] of Object.entries(updates)) {
          if (key === 'breaks') {
            const currentBreaks = currentUnsaved[key] || {}
            const newBreaks = newValue || {}
            if (JSON.stringify(currentBreaks) !== JSON.stringify(newBreaks)) {
              hasChanges = true
              break
            }
          } else if (currentUnsaved[key as keyof AttendanceShift] !== newValue) {
            hasChanges = true
            break
          }
        }
        
        if (hasChanges) {
          return {
            ...prev,
            [shiftIndex]: { ...currentUnsaved, ...updates }
          }
        }
        return prev
      })
    }, [])

    // Get current shift data
    const currentShift: ExtendedAttendanceShift | null = useMemo(() => {
      if (activeShift === null) return null

      if (shifts && activeShift < shifts.length) {
        const baseShift = shifts[activeShift]
        const changes = unsavedChanges[activeShift] || {}
        const mergedShift = { ...baseShift, ...changes }
        
        if (changes.breaks) {
          mergedShift.breaks = { ...changes.breaks }
        } else if (baseShift.breaks) {
          mergedShift.breaks = { ...baseShift.breaks }
        }
        
        return mergedShift as ExtendedAttendanceShift
      }

      if (shifts && activeShift === shifts.length) {
        const changes = unsavedChanges[activeShift] || {}
        const newShift = {
          start: undefined,
          end: undefined,
          breaks: {},
          positionId: selectedRole || Object.keys(currentCompany?.jobs || {})[0] || '',
          isConfirmed: false,
          manuallyCreated: true,
          ...changes
        } as ExtendedAttendanceShift
        
        if (changes.breaks) {
          newShift.breaks = { ...changes.breaks }
        }
        
        return newShift
      }

      return null
    }, [activeShift, shifts, unsavedChanges, selectedRole, currentCompany?.jobs])

    // Get shift analysis from context
    const shiftAnalysis = useMemo(() => {
      if (!currentShift) return null
      const shiftKey = (currentShift as any)?.shiftKey
      if (!shiftKey) return null
      return getShiftAnalysis(shiftKey)
    }, [currentShift, getShiftAnalysis])

    // Determine shift status
    const hasWarning = isNewShift ? false : currentShift ? !currentShift.positionId : false

    // Initialize selectedRole from current shift data
    useEffect(() => {
      if (currentShift?.positionId) {
        setSelectedRole(currentShift.positionId)
      }
    }, [currentShift?.positionId])

    // Auto-assign role if only one role is available
    useEffect(() => {
      if (options.length === 1 && !selectedRole) {
        const singleRole = options[0].value
        setSelectedRole(singleRole)
        if (activeShift !== null && activeShift >= shifts.length) {
          updateLocalShift(activeShift, { positionId: singleRole })
        }
      }
    }, [options, selectedRole, activeShift, shifts.length, updateLocalShift])

    // Initialize time pickers from current shift data
    useEffect(() => {
      if (currentShift?.start) {
        const startMoment = moment().startOf('day').add(currentShift.start, 'minutes')
        setRoundedTimeStart(startMoment)
      } else {
        setRoundedTimeStart(null)
      }

      if (currentShift?.end) {
        const endMoment = moment().startOf('day').add(currentShift.end, 'minutes')
        setRoundedTimeEnd(endMoment)
      } else {
        setRoundedTimeEnd(null)
      }
    }, [currentShift?.start, currentShift?.end, activeShift])

    // Show tooltip with delay
    useEffect(() => {
      if (hasWarning || (shiftAnalysis && shiftAnalysis.issues.length > 0)) {
        const timer = setTimeout(() => {
          setCanShowTooltip(true)
          setShowPlannedTooltip(true)
        }, 100)
        return () => clearTimeout(timer)
      }
    }, [hasWarning, shiftAnalysis])

    const handleClosePopover = () => {
      onClose()
      setShowPlannedTooltip(false)
      setShowClockedTooltip(false)
      setShowOverlapTooltip(false)
      setShowDeleteBreakTooltip(null)
    }

    // Calculate total hours
    const totalHours = React.useMemo(() => {
      if (!currentShift || !currentShift.start) return '0'

      const startMinutes = currentShift.start
      let endMinutes = currentShift.end

      if (!endMinutes) {
        endMinutes = startMinutes + (6.5 * 60)
      }

      let totalMinutes = endMinutes - startMinutes
      if (totalMinutes < 0) {
        totalMinutes += 24 * 60
      }

      const breakMinutes = Object.values(currentShift.breaks || {}).reduce((total, breakItem) => {
        const isUnpaid = (breakItem as any).isUnpaid !== false
        return total + (isUnpaid ? (breakItem.lengthRounded || 0) : 0)
      }, 0)

      const workMinutes = totalMinutes - breakMinutes
      const workHours = workMinutes / 60

      return Math.max(0, workHours).toFixed(1)
    }, [currentShift])

    // Handle case when employee data is not available
    if (!employee || !date) {
      return (
        <PopoverStyled {...rest} id='payroll_shift-popover' ref={ref}>
          <ContainerStyled>
            <HeaderStyled>
              <HeaderTitleStyled>Shift Details</HeaderTitleStyled>
              <CloseButtonStyled onClick={handleClosePopover}>
                <img src={closeIcon} alt='' />
              </CloseButtonStyled>
            </HeaderStyled>
            <div style={{ padding: '1rem', textAlign: 'center' }}>
              <p>Unable to load shift data.</p>
            </div>
          </ContainerStyled>
        </PopoverStyled>
      )
    }

    return (
      <>
        <PopoverStyled {...rest} id='payroll_shift-popover' ref={ref}>
          {showDeleteShiftModal && (
            <DeleteShiftModal
              onClose={() => setShowDeleteShiftModal(false)}
              onDelete={() => {
                // Handle delete logic here
                setShowDeleteShiftModal(false)
                onClose()
              }}
            />
          )}
          <ContainerStyled>
            <HeaderStyled>
              <HeaderTitleStyled>
                {dayjs(date).format('dddd D')}
                {Object.keys(unsavedChanges).length > 0 && (
                  <span style={{ color: '#ff9500', marginLeft: '8px', fontSize: '0.8em' }}>
                    • Unsaved changes
                  </span>
                )}
              </HeaderTitleStyled>
              <CloseButtonStyled onClick={handleClosePopover}>
                <img src={closeIcon} alt='' />
              </CloseButtonStyled>
            </HeaderStyled>
            
            <InfoBlockStyled>
              <AvatarInitialsStyled employee={employee} />
              <InfoBlockTextStyled>
                {employee.name} {employee.surname}
                <span style={{ fontSize: '0.8rem', color: '#666', marginLeft: '0.5rem' }}>
                  #{employee?.payrollId || employee?.customId || 'N/A'}
                </span>
              </InfoBlockTextStyled>
            </InfoBlockStyled>

            <ShiftListStyled>
              {shifts.map((shift, index) => {
                const shiftWithChanges = unsavedChanges[index] ? { ...shift, ...unsavedChanges[index] } : shift
                const shiftKey = (shift as any)?.shiftKey
                const analysis = shiftKey ? getShiftAnalysis(shiftKey) : null
                const statusColor = analysis ? getShiftStatusColor(analysis.status) : undefined

                return (
                  <ShiftListItemStyled
                    key={index}
                    $isActive={activeShift === index}
                    onClick={() => setActiveShift(index)}
                    style={{ backgroundColor: statusColor }}
                  >
                    <span>{I18n.t('common.shift')} {index + 1}</span>
                  </ShiftListItemStyled>
                )
              })}

              {shifts.length < 4 && (
                <ShiftListItemStyled
                  $isActive={activeShift === shifts.length}
                  onClick={async () => {
                    if (!onSave || !employee || !date) {
                      toast.error('Unable to create new shift: missing required data')
                      return
                    }

                    try {
                      const newShift = {
                        start: undefined,
                        end: undefined,
                        breaks: {},
                        positionId: selectedRole || Object.keys(currentCompany?.jobs || {})[0] || '',
                        isConfirmed: false,
                        manuallyCreated: true
                      }

                      const newShiftKey = `shift_${Date.now()}`
                      await onSave({ [newShiftKey]: newShift })
                      setWaitingForNewShift(shifts.length)
                      toast.success('New shift created successfully!')
                    } catch (error) {
                      console.error('Failed to create new shift:', error)
                      toast.error('Failed to create new shift')
                    }
                  }}
                >
                  {shifts.length === 0 ? 'New Shift +' : '+'}
                </ShiftListItemStyled>
              )}
            </ShiftListStyled>

            <RoleBlockStyled>
              <RoleBlockLabelStyled>{I18n.t('common.role')}</RoleBlockLabelStyled>
              <CustomSelectStyled
                options={options}
                value={options.find(option => option.value === selectedRole)}
                onChange={option => {
                  if (option) {
                    setSelectedRole(option.value as string)
                    if (activeShift !== null) {
                      updateLocalShift(activeShift, { positionId: option.value as string })
                    }
                  }
                }}
                placeholder={I18n.t('payroll.select_role')}
                $noValue={!selectedRole}
              />
            </RoleBlockStyled>

            <DividerStyled />

            <ScrollBlockStyled>
              <ShiftBlockStyled>
                <RowStyled>
                  <RowCellStyled>{I18n.t('common.shift')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.start')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('common.end')}</RowCellStyled>
                  <RowCellStyled>{I18n.t('payroll.total')}</RowCellStyled>
                </RowStyled>
                
                <RowStyled>
                  <RowCellStyled>{I18n.t('payroll.rounded')}</RowCellStyled>
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setRoundedTimeStart(value)
                      if (activeShift !== null) {
                        const newStartMinutes = value ? value.hour() * 60 + value.minute() : undefined
                        updateLocalShift(activeShift, { start: newStartMinutes })
                      }
                    }}
                    value={roundedTimeStart}
                    placeholder='-:-'
                    hideArrow
                    $noValue={!roundedTimeStart}
                    isFixedMenu
                  />
                  <CustomTimePickerStyled
                    minuteStep={1}
                    onChange={value => {
                      setRoundedTimeEnd(value)
                      if (activeShift !== null) {
                        const newEndMinutes = value ? value.hour() * 60 + value.minute() : undefined
                        updateLocalShift(activeShift, { end: newEndMinutes })
                      }
                    }}
                    value={roundedTimeEnd}
                    placeholder='-:-'
                    hideArrow
                    $noValue={!roundedTimeEnd}
                    isFixedMenu
                  />
                  <RowCellStyled>
                    {shiftAnalysis && shiftAnalysis.issues.length > 0 && (
                      <WarningButtonStyled>
                        <WarningIconStyled />
                      </WarningButtonStyled>
                    )}
                    {totalHours}{I18n.t('common.hours_shorten').toLowerCase()}
                  </RowCellStyled>
                </RowStyled>
              </ShiftBlockStyled>
            </ScrollBlockStyled>
          </ContainerStyled>
        </PopoverStyled>
      </>
    )
  }
)

ShiftPopoverSimplified.displayName = 'ShiftPopoverSimplified'

export default ShiftPopoverSimplified
