import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import Overlay from 'react-bootstrap/Overlay'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import cloneDeep from 'lodash/cloneDeep'
import forEach from 'lodash/forEach'
import isEmpty from 'lodash/isEmpty'
import { RootState } from 'store/reducers'

import CalendarPopover from './components/CalendarPopover'
import ConflictShiftModal from './components/ConflictShiftModal/ConflictShiftModal'
import HoursTable from './components/HoursTable'
import RollingNumber from './components/RollingNumber'
import { usePeriod } from 'contexts/PeriodContext'
import { 
  ShiftConflictProvider, 
  useShiftConflict, 
  ConflictResolution 
} from 'contexts/ShiftConflictContext'
import { convertLegacyConflictsToContextFormat, convertConflictDataToModalFormat } from 'utils/shiftConflictContext'

import {
  TabButtonStyled,
  WeekBlockWrapStyled,
  WeekBlockStyled,
  WeekTopBlockStyled,
  WeekTopBlockRowStyled,
  PeriodBlockStyled,
  CalendarButtonStyled,
  CalendarIconStyled,
  PeriodSliderStyled,
  PeriodArrowStyled,
  PeriodTextStyled,
  PeriodStatusStyled,
  WeekPeriodTabsStyled,
  ButtonStyled,
  ClockIconStyled,
  WeekDaysStyled,
  DayStyled,
  GreyOverlayStyled,
  ClaimedStatusStyled,
  ClaimedStatusBlockStyled,
  ClaimedTextStyled,
  SparkleIconStyled,
  ErrorContainer,
  LoadingContainer
} from '../styles/Hours.styles'

import { database, useAppContext } from '../../../index'
import PayrollConflictModal from '../../PayrollOld/modals/PayrollConflictModal'
import { matchClockInWithScheduledShift } from '../../PayrollOld/payrollUtils'

import {
  getCurrentUserInfo,
  logShiftChanges,
  logShiftDeletion
} from 'utils/payroll/activityLogger'
import {
  RoleFilterState,
  clearRoleFilterState,
  createInitialFilterState,
  extractRolesFromCompany,
  getSingleRoleInfo,
  isSingleBOHRole,
  loadRoleFilterState,
  saveRoleFilterState
} from 'utils/payroll/roleFilterUtils'
import {
  analyzeShiftConflicts
} from 'utils/payroll/shiftConflictUtils'

import {
  AttendanceSettings,
  AttendanceShift,
  AttendanceShifts,
  IAllConflictingAttendanceShifts
} from 'types/attendance'
import { Company, IPosition } from 'types/company'
import { IEmployee } from 'types/employee'

import calendarIcon from 'img/icons/calendarBlankIcon.svg'
import ArrowLeft from 'img/IconsHover/ArrowLeftGrey'
import ArrowRight from 'img/IconsHover/ArrowRightGrey'

// import noteIcon from 'img/icons/noteIcon.svg'



const renderWeekDays = ({
  activeWeekPeriodTab,
  filteredPeriodData,
  staticWeekDays
}: {
  activeWeekPeriodTab: string
  filteredPeriodData: { startOfPeriod: dayjs.Dayjs; payrollLength: number }
  staticWeekDays: Array<{ dayName: string; displayText: string }>
}) => {
  const days = []
  const today = dayjs()

  // Always show 7 days (one week) for all tabs, just indicate current day
  for (let i = 0; i < 7; i++) {
    const isToday = staticWeekDays[i].dayName === today.format('dddd')
    days.push(
      <DayStyled
        $isToday={isToday}
        key={i}
      >
        {staticWeekDays[i].displayText}
      </DayStyled>
    )
  }
  return days
}

interface HoursProps {
  employeesArray: IEmployee[]
  employeesByRole: {
    [roleId: string]: { role: IPosition; employees: IEmployee[] }
  }
  searchTerm: string
  setSearchTerm: (value: string) => void
  selectedPositionId: string
  setSelectedPositionId: (value: string) => void
  currentCompany: Company
  displayBy: string
  setDisplayBy: (value: string) => void
  displayByArray: Array<{ id: string; label: string; icon: React.ReactNode }>
  onSearchEmployee: (value: string) => void
  attendanceSettings: AttendanceSettings
  setAttendanceSettings: (settings: AttendanceSettings) => void
  hasPayrollIntegration: boolean
}

interface HoursInnerProps extends HoursProps {
  onSave: (
    newShift: { [key: string]: AttendanceShift },
    employeeId: string,
    date: string,
    attendanceData: AttendanceShifts,
    setAttendanceData: React.Dispatch<React.SetStateAction<AttendanceShifts>>
  ) => Promise<void>
  onDeleteShift: (
    shiftKey: string,
    employeeId: string,
    date: string,
    attendanceData: AttendanceShifts,
    setAttendanceData: React.Dispatch<React.SetStateAction<AttendanceShifts>>
  ) => Promise<void>
  onResolveConflictCallback?: (conflictId: string, resolution: ConflictResolution) => Promise<void>
}

const Hours: React.FC<HoursProps> = ({
  employeesArray,
  employeesByRole,
  searchTerm,
  setSearchTerm: _setSearchTerm,
  selectedPositionId,
  setSelectedPositionId: _setSelectedPositionId,
  currentCompany,
  displayBy,
  setDisplayBy,
  displayByArray,
  onSearchEmployee,
  attendanceSettings,
  setAttendanceSettings: _setAttendanceSettings,
  hasPayrollIntegration
}) => {
  const { user, currentEmployee } = useAppContext()


  // Save shift function (ported from PayrollOld)
  const onSave = async (
    newShift: { [key: string]: AttendanceShift },
    employeeId: string,
    date: string,
    attendanceData: AttendanceShifts,
    setAttendanceData: React.Dispatch<React.SetStateAction<AttendanceShifts>>
  ) => {
    const copy = cloneDeep(attendanceData)

    if (!copy[date]) {
      copy[date] = {}
    }

    if (!copy[date][employeeId]) {
      copy[date][employeeId] = {}
    }

    // Get old shift data for activity logging
    const oldShifts = copy[date][employeeId] || {}

    // Update the shifts
    copy[date][employeeId] = {
      ...copy[date][employeeId],
      ...newShift
    }

    // Log activity for each changed shift
    const userInfo = getCurrentUserInfo(user, currentEmployee)
    for (const [shiftKey, shiftData] of Object.entries(newShift)) {
      const oldShift = oldShifts[shiftKey] || null
      await logShiftChanges(
        currentCompany.key,
        date,
        employeeId,
        oldShift,
        shiftData,
        shiftKey,
        userInfo.id,
        userInfo.name,
        userInfo.avatar
      )
    }

    setAttendanceData(copy)

    // Persist changes to Firebase database
    try {
      const updates: { [path: string]: any } = {}
      for (const [shiftKey, shiftData] of Object.entries(newShift)) {
        updates[
          `Attendance/${currentCompany.key}/${date}/${employeeId}/${shiftKey}`
        ] = shiftData
      }
      await database.ref().update(updates)
    } catch (error) {
      console.error('Failed to save shift to database:', error)
    }
  }

  // Delete shift function (ported from PayrollOld)
  const onDeleteShift = async (
    shiftKey: string,
    employeeId: string,
    date: string,
    attendanceData: AttendanceShifts,
    setAttendanceData: React.Dispatch<React.SetStateAction<AttendanceShifts>>
  ) => {
    const copy = cloneDeep(attendanceData)

    if (copy[date] && copy[date][employeeId]) {
      // Get the shift data before deletion for activity logging
      const deletedShift = copy[date][employeeId][shiftKey]

      if (deletedShift) {
        // Log the deletion activity
        const userInfo = getCurrentUserInfo(user, currentEmployee)
        await logShiftDeletion(
          currentCompany.key,
          date,
          employeeId,
          deletedShift,
          shiftKey,
          userInfo.id,
          userInfo.name,
          userInfo.avatar
        )
      }

      delete copy[date][employeeId][shiftKey]

      // Persist deletion to Firebase database
      try {
        await database
          .ref(
            `Attendance/${currentCompany.key}/${date}/${employeeId}/${shiftKey}`
          )
          .remove()
      } catch (error) {
        console.error('Failed to delete shift from database:', error)
      }
    }

    setAttendanceData(copy)
  }

  // Enhanced conflict resolution handler for additional logging
  const handleResolveConflictCallback = useCallback(async (conflictId: string, resolution: ConflictResolution) => {
    // Additional custom logic like advanced activity logging can be added here
    console.log('Conflict resolved with additional logging:', conflictId, resolution)
  }, [])

  return (
    <HoursInner
      employeesArray={employeesArray}
      employeesByRole={employeesByRole}
      searchTerm={searchTerm}
      setSearchTerm={_setSearchTerm}
      selectedPositionId={selectedPositionId}
      setSelectedPositionId={_setSelectedPositionId}
      currentCompany={currentCompany}
      displayBy={displayBy}
      setDisplayBy={setDisplayBy}
      displayByArray={displayByArray}
      onSearchEmployee={onSearchEmployee}
      attendanceSettings={attendanceSettings}
      setAttendanceSettings={_setAttendanceSettings}
      hasPayrollIntegration={hasPayrollIntegration}
      onSave={onSave}
      onDeleteShift={onDeleteShift}
      onResolveConflictCallback={handleResolveConflictCallback}
    />
  )
}

const HoursInner: React.FC<HoursInnerProps> = (props) => {
  // Create stable Firebase operation wrappers - these will be properly implemented by the inner component
  const handleSaveShift = useCallback(async (
    newShift: { [key: string]: AttendanceShift },
    employeeId: string,
    date: string
  ) => {
    // This is a placeholder - the actual implementation will be handled by the onSave prop
    console.log('Save shift operation requested:', { newShift, employeeId, date })
  }, [])

  const handleDeleteShift = useCallback(async (
    shiftKey: string,
    employeeId: string,
    date: string
  ) => {
    // This is a placeholder - the actual implementation will be handled by the onDeleteShift prop
    console.log('Delete shift operation requested:', { shiftKey, employeeId, date })
  }, [])

  return (
    <ShiftConflictProvider 
      onResolveConflictCallback={props.onResolveConflictCallback}
      onSaveShift={handleSaveShift}
      onDeleteShift={handleDeleteShift}
      companyKey={props.currentCompany.key}
    >
      <HoursInnerContent {...props} />
    </ShiftConflictProvider>
  )
}

const HoursInnerContent: React.FC<HoursInnerProps> = ({
  employeesArray,
  employeesByRole,
  searchTerm,
  setSearchTerm: _setSearchTerm,
  selectedPositionId,
  setSelectedPositionId: _setSelectedPositionId,
  currentCompany,
  displayBy,
  setDisplayBy,
  displayByArray,
  onSearchEmployee,
  attendanceSettings,
  setAttendanceSettings: _setAttendanceSettings,
  hasPayrollIntegration,
  onSave: onSaveProp,
  onDeleteShift: onDeleteShiftProp
}) => {
  // Use the shift conflict context
  const { actions: conflictActions } = useShiftConflict()

  // Refs for tracking state  
  const previousConflictCount = useRef<number>(0)

  // Save shift function (ported from PayrollOld)
  const onSave = async (
    newShift: { [key: string]: AttendanceShift },
    employeeId: string,
    date: string
  ) => {
    await onSaveProp(newShift, employeeId, date, attendanceData, setAttendanceData)
  }

  // Delete shift function (ported from PayrollOld)
  const onDeleteShift = async (
    shiftKey: string,
    employeeId: string,
    date: string
  ) => {
    await onDeleteShiftProp(shiftKey, employeeId, date, attendanceData, setAttendanceData)
  }
  // Use period context for all period-related data
  const { period, actions } = usePeriod()
  const {
    basePeriodStart: startOfPeriod,
    payrollLength,
    currentPeriodOffset,
    currentPeriodStart,
    currentPeriodEnd,
    periodDisplay,
    startOfPeriodStr,
    endOfPeriodStr
  } = period

  // Attendance data state (will be loaded based on period context)
  const [attendanceData, setAttendanceData] = useState<AttendanceShifts>({})
  const [isDataLoaded, setIsDataLoaded] = useState(false)
  const [schedule, setSchedule] = useState<any>({}) // Schedule data for conflict detection
  const [isScheduleLoaded, setIsScheduleLoaded] = useState(false)

  // Error handling and loading states
  const [dataError, setDataError] = useState<string | null>(null)
  const [scheduleError, setScheduleError] = useState<string | null>(null)
  const [isRetrying, setIsRetrying] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const maxRetries = 3

  // Error retry handler
  const handleRetry = useCallback(() => {
    setDataError(null)
    setScheduleError(null)
    setRetryCount(0)
    // Trigger data reload by updating a dependency
    window.location.reload()
  }, [])

  // Network connectivity monitoring
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      // Auto-retry when connection is restored
      if ((dataError || scheduleError) && !isRetrying) {
        handleRetry()
      }
    }
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [dataError, scheduleError, isRetrying, handleRetry])

  // Auto-retry when user returns to the page (visibility API)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && (dataError || scheduleError) && !isRetrying && isOnline) {
        // User returned to the page and we have errors - auto retry
        setTimeout(() => {
          if ((dataError || scheduleError) && !isRetrying) {
            console.log('Auto-retrying due to page visibility change')
            handleRetry()
          }
        }, 1000) // Small delay to avoid immediate retry
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [dataError, scheduleError, isRetrying, isOnline, handleRetry])

  // Retry utility function with exponential backoff
  const retryWithBackoff = useCallback(async (
    operation: () => Promise<void>,
    attempt: number = 1
  ): Promise<void> => {
    try {
      await operation()
    } catch (error) {
      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000) // Max 10 seconds
        console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms...`, error)

        setIsRetrying(true)
        setRetryCount(attempt)

        await new Promise(resolve => setTimeout(resolve, delay))
        return retryWithBackoff(operation, attempt + 1)
      } else {
        setIsRetrying(false)
        setRetryCount(0)
        throw error
      }
    }
  }, [maxRetries, setIsRetrying, setRetryCount])

  // Load attendance data and schedule data when period changes
  useEffect(() => {
    let attendanceRef: ReturnType<typeof database.ref> | null = null
    let scheduleRef: ReturnType<typeof database.ref> | null = null
    let isMounted = true

    const loadAttendanceData = (): Promise<void> => {
      return new Promise((resolve, reject) => {
        try {
          const attendanceQuery = database
            .ref('Attendance/' + currentCompany.key)
            .orderByKey()
            .startAt(startOfPeriodStr)
            .endAt(endOfPeriodStr)

          attendanceRef = database.ref('Attendance/' + currentCompany.key)

          const onValue = (snapshot: any) => {
            if (!isMounted) return

            try {
              const data = snapshot.val() || {}
              setAttendanceData(data)
              setIsDataLoaded(true)
              setDataError(null)
              resolve()
            } catch (error) {
              console.error('Error processing attendance data:', error)
              reject(new Error('Failed to process attendance data'))
            }
          }

          const onError = (error: any) => {
            console.error('Firebase attendance error:', error)
            reject(new Error(`Firebase attendance error: ${error.message || 'Unknown error'}`))
          }

          attendanceQuery.on('value', onValue, onError)
        } catch (error) {
          console.error('Error setting up attendance listener:', error)
          reject(new Error('Failed to setup attendance data listener'))
        }
      })
    }

    const loadScheduleData = (): Promise<void> => {
      return new Promise((resolve, reject) => {
        try {
          const scheduleQuery = database
            .ref('WeeklySchedule/' + currentCompany.key)
            .orderByKey()
            .startAt(startOfPeriodStr)
            .endAt(endOfPeriodStr)

          scheduleRef = database.ref('WeeklySchedule/' + currentCompany.key)

          const onValue = (snapshot: any) => {
            if (!isMounted) return

            try {
              const data = snapshot.val() || {}
              setSchedule(data)
              setIsScheduleLoaded(true)
              setScheduleError(null)
              resolve()
            } catch (error) {
              console.error('Error processing schedule data:', error)
              reject(new Error('Failed to process schedule data'))
            }
          }

          const onError = (error: any) => {
            console.error('Firebase schedule error:', error)
            reject(new Error(`Firebase schedule error: ${error.message || 'Unknown error'}`))
          }

          scheduleQuery.on('value', onValue, onError)
        } catch (error) {
          console.error('Error setting up schedule listener:', error)
          reject(new Error('Failed to setup schedule data listener'))
        }
      })
    }

    const loadData = async () => {
      if (!currentCompany.key) return

      try {
        // Reset error states
        setDataError(null)
        setScheduleError(null)
        setIsRetrying(false)
        setRetryCount(0)

        // Load both attendance and schedule data with retry logic
        await Promise.all([
          retryWithBackoff(loadAttendanceData).catch(error => {
            console.error('Failed to load attendance data after retries:', error)
            setDataError(error.message || 'Failed to load attendance data')
          }),
          retryWithBackoff(loadScheduleData).catch(error => {
            console.error('Failed to load schedule data after retries:', error)
            setScheduleError(error.message || 'Failed to load schedule data')
          })
        ])
      } catch (error) {
        console.error('Unexpected error during data loading:', error)
      } finally {
        setIsRetrying(false)
        setRetryCount(0)
      }
    }

    loadData()

    return () => {
      isMounted = false
      if (attendanceRef) {
        attendanceRef.off()
      }
      if (scheduleRef) {
        scheduleRef.off()
      }
      setIsScheduleLoaded(false)
      setIsDataLoaded(false)
      setDataError(null)
      setScheduleError(null)
      setIsRetrying(false)
      setRetryCount(0)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentCompany.key, startOfPeriodStr, endOfPeriodStr]) // retryWithBackoff is stable

  // Role filtering state and logic
  const departmentRoles = useMemo(
    () => extractRolesFromCompany(currentCompany.jobs || {}),
    [currentCompany.jobs]
  )

  const isSingleRole = useMemo(
    () => isSingleBOHRole(departmentRoles),
    [departmentRoles]
  )

  const singleRoleInfo = useMemo(
    () => getSingleRoleInfo(departmentRoles),
    [departmentRoles]
  )

  const [roleFilterState, setRoleFilterState] = useState<RoleFilterState>(
    () => {
      // Initialize with empty state first, will be properly set in useEffect
      return {
        selectedDepartments: [],
        selectedRoles: {},
        selectedSubcategories: {}
      }
    }
  )

  // Initialize filter state when department roles are available
  useEffect(() => {
    // Only proceed if we have department roles data
    if (
      departmentRoles.FOH.length === 0 &&
      departmentRoles.BOH.length === 0 &&
      departmentRoles.MNG.length === 0
    ) {
      return
    }

    // Clear any potentially corrupted saved state first
    clearRoleFilterState()

    const defaultState = createInitialFilterState(departmentRoles)
    setRoleFilterState(defaultState)
  }, [departmentRoles])

  // Handle view switching and saved state
  useEffect(() => {
    if (displayBy !== 'roles') {
      // Reset to default (all roles selected) when switching to "By Employee"
      clearRoleFilterState()
      if (
        departmentRoles.FOH.length > 0 ||
        departmentRoles.BOH.length > 0 ||
        departmentRoles.MNG.length > 0
      ) {
        const defaultState = createInitialFilterState(departmentRoles)
        setRoleFilterState(defaultState)
      }
    } else {
      // Load saved state when switching to "By Roles" - only run once when switching to roles view
      const saved = loadRoleFilterState()
      if (saved && saved.selectedDepartments.length > 0) {
        setRoleFilterState(saved)
      } else if (
        departmentRoles.FOH.length > 0 ||
        departmentRoles.BOH.length > 0 ||
        departmentRoles.MNG.length > 0
      ) {
        // Fallback to default state if no saved state
        const defaultState = createInitialFilterState(departmentRoles)
        setRoleFilterState(defaultState)
      }
    }
  }, [displayBy, departmentRoles]) // Removed roleFilterState.selectedDepartments.length from dependencies

  const handleRoleFilterChange = (newState: RoleFilterState) => {
    // Always update the local state immediately
    setRoleFilterState(newState)

    // Save to localStorage when in roles view
    if (displayBy === 'roles') {
      saveRoleFilterState(newState)
    }
  }

  const weekPeriodTabs = [
    { id: 'biweekly', label: I18n.t('payroll.biweekly') },
    { id: 'week1', label: I18n.t('payroll.week') + ' 1' },
    { id: 'week2', label: I18n.t('payroll.week') + ' 2' }
  ]
  const [activeWeekPeriodTab, setActiveWeekPeriodTab] = useState('biweekly')
  // Debug: log tab changes
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('[DEBUG] activeWeekPeriodTab:', activeWeekPeriodTab)
    }
  }, [activeWeekPeriodTab])

  const isLocaleFr =
    useSelector((state: RootState) => state.i18n.locale) === 'fr'

  // Use period display from context, adjusted for active week tab
  const currentDisplayPeriod = React.useMemo(() => {
    // Calculate effective period based on active week tab
    let effectiveStart = currentPeriodStart
    let effectiveEnd = currentPeriodEnd

    if (activeWeekPeriodTab === 'biweekly') {
      // Always use 14 days for biweekly
      effectiveStart = currentPeriodStart
      effectiveEnd = currentPeriodStart.clone().add(13, 'days')
    } else if (activeWeekPeriodTab === 'week1') {
      // Week 1: First 7 days of the bi-weekly period
      effectiveStart = currentPeriodStart
      effectiveEnd = currentPeriodStart.clone().add(6, 'days')
    } else if (activeWeekPeriodTab === 'week2') {
      // Week 2: Second 7 days of the bi-weekly period
      effectiveStart = currentPeriodStart.clone().add(7, 'days')
      effectiveEnd = currentPeriodStart.clone().add(13, 'days')
    }

    return {
      start: effectiveStart,
      end: effectiveEnd,
      startStr: effectiveStart.format(isLocaleFr ? 'DD MMM' : 'MMM DD'),
      endStr: effectiveEnd.format(isLocaleFr ? 'DD MMM' : 'MMM DD'),
      isCurrentPeriod: periodDisplay.isCurrentPeriod,
      isPastPeriod: periodDisplay.isPastPeriod,
      isFuturePeriod: periodDisplay.isFuturePeriod
    }
  }, [currentPeriodStart, currentPeriodEnd, activeWeekPeriodTab, isLocaleFr, periodDisplay])

  const isPastPeriod = currentDisplayPeriod.isPastPeriod
  const isFuturePeriod = currentDisplayPeriod.isFuturePeriod

  // Helper function to convert context offset to display offset for CalendarPopover
  const getDisplayOffsetForCalendarPopover = React.useCallback(() => {
    // Convert context offset to display offset for CalendarPopover
    if ((activeWeekPeriodTab === 'biweekly' || activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') && payrollLength === 7) {
      // Convert weekly offset back to biweekly offset for all tab types
      return Math.floor(currentPeriodOffset / 2)
    }
    return currentPeriodOffset
  }, [activeWeekPeriodTab, payrollLength, currentPeriodOffset])

  // Handle period navigation from arrow buttons (works with context offsets)
  const handlePeriodNavigation = (direction: 'prev' | 'next') => {
    const increment = direction === 'next' ? 1 : -1
    let newOffset = currentPeriodOffset + increment

    // For biweekly/week1/week2 tabs with weekly payroll, move by 2 to stay aligned with biweekly periods
    if ((activeWeekPeriodTab === 'biweekly' || activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') && payrollLength === 7) {
      newOffset = currentPeriodOffset + (increment * 2)
    }

    // Update the user period offset in context
    actions.setUserPeriodOffset(newOffset)
  }

  // Handle period selection from CalendarPopover (works with display offsets)
  const handlePeriodSelect = (offset: number) => {
    // When activeWeekPeriodTab is biweekly, week1, or week2 but payrollLength is 7 (weekly),
    // we need to adjust the offset to account for the difference
    let adjustedOffset = offset

    if ((activeWeekPeriodTab === 'biweekly' || activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') && payrollLength === 7) {
      // Convert biweekly offset to weekly offset
      // Each biweekly period = 2 weekly periods
      adjustedOffset = offset * 2
    }

    // Update the user period offset in context
    actions.setUserPeriodOffset(adjustedOffset)
  }

  // Calculate filtered period data based on active week tab
  const filteredPeriodData = useMemo(() => {
    // Calculate the effective period length and offset based on active tab
    let effectivePayrollLength = payrollLength
    let effectiveOffset = currentPeriodOffset

    if (activeWeekPeriodTab === 'biweekly' && payrollLength === 7) {
      // When viewing biweekly but payroll is weekly, convert offset
      effectivePayrollLength = 14
      effectiveOffset = Math.floor(currentPeriodOffset / 2)
    } else if ((activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') && payrollLength === 14) {
      // When viewing weekly but payroll is biweekly, convert offset
      effectivePayrollLength = 14
      effectiveOffset = currentPeriodOffset
    }

    // Use fixed startOfPeriod + offset approach
    const basePeriodStart = startOfPeriod
      .clone()
      .add(effectiveOffset * effectivePayrollLength, 'days')

    let filteredStartOfPeriod = basePeriodStart
    let filteredPayrollLength = effectivePayrollLength

    if (activeWeekPeriodTab === 'biweekly') {
      // Always use 14 days for biweekly
      filteredPayrollLength = 14
    } else if (activeWeekPeriodTab === 'week1') {
      // First week of the biweekly period
      filteredPayrollLength = 7
    } else if (activeWeekPeriodTab === 'week2') {
      // Second week of the biweekly period
      filteredStartOfPeriod = basePeriodStart.clone().add(7, 'days')
      filteredPayrollLength = 7
    }

    return {
      startOfPeriod: filteredStartOfPeriod,
      payrollLength: filteredPayrollLength
    }
  }, [startOfPeriod, payrollLength, currentPeriodOffset, activeWeekPeriodTab])


  const staticWeekDays = useMemo(() => {
    // Monday = 1, Sunday = 7
    const weekKeys = [
      'jobs.Monday',
      'jobs.Tuesday',
      'jobs.Wednesday',
      'jobs.Thursday',
      'jobs.Friday',
      'jobs.Saturday',
      'jobs.Sunday'
    ]
    const weekStartingDay = currentCompany.payrollStartingDay || 'Monday'
    const startingIndex = weekKeys.indexOf('jobs.' + weekStartingDay)
    weekKeys.push(...weekKeys.splice(0, startingIndex))
    return weekKeys.map(key => {
      const dayName = I18n.t(key)
      return {
        dayName,
        displayText: dayName
      }
    })
  }, [currentCompany.payrollStartingDay])
  
  // Filter attendance data based on the selected week tab
  const filteredAttendanceData = useMemo(() => {
    if (activeWeekPeriodTab === 'biweekly') {
      return attendanceData // Return all data for biweekly view
    }

    const filtered: AttendanceShifts = {}
    const { startOfPeriod: periodStart, payrollLength: periodLength } =
      filteredPeriodData

    // Generate date keys for the filtered period
    for (let i = 0; i < periodLength; i++) {
      const dateKey = periodStart.clone().add(i, 'days').format('YYYY-MM-DD')
      if (attendanceData[dateKey]) {
        filtered[dateKey] = attendanceData[dateKey]
      }
    }

    return filtered
  }, [attendanceData, activeWeekPeriodTab, filteredPeriodData])

  // Calculate conflicting shifts for the current period and update context
  const allConflictingShifts = useMemo(() => {
    const conflicts: IAllConflictingAttendanceShifts = {}
    const currentPeriodStart = startOfPeriod
      .clone()
      .add(currentPeriodOffset * payrollLength, 'days')
    const currentPeriodEnd = currentPeriodStart
      .clone()
      .add(payrollLength - 1, 'days')

    // Iterate through each day in the current period
    let currentDay = currentPeriodStart.clone()

    while (currentDay.isSameOrBefore(currentPeriodEnd)) {
      const dateKey = currentDay.format('YYYY-MM-DD')
      const dayShifts = attendanceData[dateKey]

      if (dayShifts) {
        const currentDayCopy = currentDay.clone()
        forEach(dayShifts, (employeeShifts, employeeId) => {
          forEach(employeeShifts, (shift, shiftKey) => {
            if (!shift.isConfirmed) {
              // Apply rounding to shift times (matching PayrollOld logic)
              const roundingTime = attendanceSettings?.roundingTime || 15
              const shiftStartRounded = shift.start ? Math.round(shift.start / roundingTime) * roundingTime : 0
              const shiftEndRounded = shift.end ? Math.round(shift.end / roundingTime) * roundingTime : null

              // Find matching scheduled shift using actual schedule data
              const scheduledPositions = schedule?.[dateKey]?.[employeeId] || {}
              let scheduledShift = null

              if (!isEmpty(scheduledPositions) && shift.start !== undefined) {
                const dayShifts: any[] = []

                forEach(scheduledPositions, (subpositions, positionId) => {
                  forEach(subpositions, (subpositionShifts, subcategoryId) => {
                    forEach(subpositionShifts, (scheduledShiftData, shiftKey) => {
                      dayShifts.push({
                        ...scheduledShiftData,
                        positionId,
                        subcategoryId,
                        shiftKey
                      })
                    })
                  })
                })

                const matchedShift = matchClockInWithScheduledShift({
                  dayShifts,
                  shiftStartRounded,
                  defaultDuration: currentCompany?.defaultDuration || 480,
                  shift: {
                    start: shiftStartRounded,
                    end: shiftEndRounded
                  }
                })

                if (matchedShift) {
                  scheduledShift = matchedShift
                }
              }

              // Analyze shift conflicts
              const conflictAnalysis = analyzeShiftConflicts(
                shift,
                shiftStartRounded,
                shiftEndRounded,
                scheduledShift,
                currentDayCopy.isSame(dayjs(), 'day'),
                false // TODO: Calculate actual working status
              )

              if (conflictAnalysis.isConflictingShift) {
                if (!conflicts[dateKey]) {
                  conflicts[dateKey] = {}
                }
                if (!conflicts[dateKey][employeeId]) {
                  conflicts[dateKey][employeeId] = {}
                }

                conflicts[dateKey][employeeId][shiftKey] = {
                  ...shift,
                  neverClockedOut: conflictAnalysis.notClockedOut,
                  scheduledShift,
                  missingPosition: !shift.positionId,
                  isClockInDifferent: conflictAnalysis.isClockInDifferent,
                  isClockOutDiffrent: conflictAnalysis.isClockOutDifferent,
                  shiftStartRounded,
                  shiftEndRounded: shiftEndRounded || 0
                }
              }
            }
          })
        })
      }

      currentDay = currentDay.add(1, 'day')
    }

    return conflicts
  }, [
    attendanceData,
    schedule,
    startOfPeriod,
    currentPeriodOffset,
    payrollLength,
    currentCompany,
    attendanceSettings?.roundingTime
  ])

  // Update context when conflicts change
  useEffect(() => {
    if (!conflictActions) return
    
    const conflictsArray = convertLegacyConflictsToContextFormat(allConflictingShifts)
    conflictActions.setConflicts(conflictsArray)
  }, [allConflictingShifts, conflictActions])

  // Count total number of conflicts (excluding newly created manual shifts)
  const numberOfConflicts = useMemo(() => {
    if (!conflictActions) return 0
    
    const unresolvedCount = conflictActions.getUnresolvedConflicts().length
    // Only log when count changes to avoid spam
    if (unresolvedCount !== previousConflictCount.current) {
      console.log('📊 Number of conflicts changed:', {
        from: previousConflictCount.current,
        to: unresolvedCount
      })
      previousConflictCount.current = unresolvedCount
    }
    return unresolvedCount
  }, [conflictActions])

  const [showCalendar, setShowCalendar] = useState(false)
  const calendarRef = useRef<HTMLDivElement>(null)
  const [showConflictModal, setShowConflictModal] = useState(false)
  const [hasAutoOpenedConflicts, setHasAutoOpenedConflicts] = useState(false)

  // Auto-open conflict modal when entering Hours module if conflicts are present
  useEffect(() => {
    if (numberOfConflicts > 0 && !hasAutoOpenedConflicts && !showConflictModal) {
      setShowConflictModal(true)
      setHasAutoOpenedConflicts(true)
    }
  }, [numberOfConflicts, hasAutoOpenedConflicts, showConflictModal])

  // Reset auto-open flag when conflicts are resolved
  useEffect(() => {
    if (numberOfConflicts === 0) {
      setHasAutoOpenedConflicts(false)
    }
  }, [numberOfConflicts])

  const claimedAmount = 8114.76

  const [animationKey, setAnimationKey] = useState(0)
  const handleMouseEnter = () => {
    setAnimationKey(prev => prev + 1) // triggers rerender
  }


  // Graceful degradation - determine if we should show degraded view
  const shouldShowDegradedView = (dataError || scheduleError) && retryCount >= maxRetries
  const hasPartialData = isDataLoaded || isScheduleLoaded

  return (
    <>
      {/* Error Display */}
      {(dataError || scheduleError) && (
        <ErrorContainer>
          <div className="error-content">
            <div className="error-icon">⚠️</div>
            <div className="error-details">
              <div className="error-title">
                {shouldShowDegradedView ? 'Running in Limited Mode' : 'Data Loading Error'}
              </div>
              {!isOnline && (
                <div className="error-message">
                  <strong>No internet connection</strong> - Please check your network
                </div>
              )}
              {dataError && (
                <div className="error-message">
                  Attendance: {dataError}
                  {!isOnline && ' (offline)'}
                </div>
              )}
              {scheduleError && (
                <div className="error-message">
                  Schedule: {scheduleError}
                  {!isOnline && ' (offline)'}
                </div>
              )}
              {isRetrying && (
                <div className="retry-info">
                  Retrying... (Attempt {retryCount}/{maxRetries})
                </div>
              )}
              {shouldShowDegradedView && hasPartialData && (
                <div className="degraded-info">
                  Some features may be limited. Showing available data.
                </div>
              )}
              {shouldShowDegradedView && !hasPartialData && (
                <div className="degraded-info">
                  Unable to load data. Please check your connection and try again.
                </div>
              )}
            </div>
            <button className="retry-button" onClick={handleRetry} disabled={isRetrying}>
              {isRetrying ? 'Retrying...' : shouldShowDegradedView ? 'Try Again' : 'Retry'}
            </button>
          </div>
        </ErrorContainer>
      )}

      {/* Loading Indicator */}
      {(!isDataLoaded || !isScheduleLoaded) && !dataError && !scheduleError && (
        <LoadingContainer>
          <div className="loading-content">
            <div className="loading-spinner"></div>
            <div className="loading-text">
              Loading payroll data...
              {isRetrying && ` (Retry ${retryCount}/${maxRetries})`}
            </div>
          </div>
        </LoadingContainer>
      )}

      <WeekBlockWrapStyled>
        <WeekBlockStyled>
          <WeekTopBlockStyled>
            <WeekTopBlockRowStyled>
              <PeriodBlockStyled ref={calendarRef}>
                <CalendarButtonStyled
                  onClick={() => setShowCalendar(!showCalendar)}
                >
                  <CalendarIconStyled
                    src={calendarIcon}
                    alt=''
                  />
                </CalendarButtonStyled>

                <PeriodSliderStyled>
                  
                  <PeriodArrowStyled
                    onClick={() => handlePeriodNavigation('prev')}
                  >
                    <ArrowLeft />
                  </PeriodArrowStyled>
                  <PeriodTextStyled
                    onClick={() => setShowCalendar(!showCalendar)}
                    $isLowerCase={isLocaleFr}
                  >
                    {currentDisplayPeriod.startStr} -{' '}
                    {currentDisplayPeriod.endStr}
                  </PeriodTextStyled>
                    <PeriodArrowStyled
                    onClick={() => handlePeriodNavigation('next')}
                  >
                    <ArrowRight />
                  </PeriodArrowStyled>
                </PeriodSliderStyled>
                <PeriodStatusStyled
                  $isCurrentPeriod={!isPastPeriod && !isFuturePeriod}
                  onClick={() => setShowCalendar(!showCalendar)}
                >
                  {isPastPeriod
                    ? I18n.t('payroll.past_period')
                    : isFuturePeriod
                      ? I18n.t('payroll.upcoming')
                      : I18n.t('payroll.current_period')}
                </PeriodStatusStyled>
              </PeriodBlockStyled>

              {showCalendar && (
                <GreyOverlayStyled onClick={() => setShowCalendar(false)} />
              )}
              <Overlay
                rootClose
                show={showCalendar}
                placement='bottom'
                onHide={() => setShowCalendar(false)}
                target={() => calendarRef.current}
              >
                <CalendarPopover
                  onClose={() => setShowCalendar(false)}
                  startOfPeriod={startOfPeriod}
                  payrollLength={payrollLength}
                  currentPeriodOffset={getDisplayOffsetForCalendarPopover()}
                  onPeriodSelect={handlePeriodSelect}
                  attendanceSettings={attendanceSettings}
                  payrollStartingDay={currentCompany.payrollStartingDay}
                  activeWeekPeriodTab={activeWeekPeriodTab}
                />
              </Overlay>

              <WeekPeriodTabsStyled role='tablist'>
                {weekPeriodTabs.map(tab => (
                  <TabButtonStyled
                    key={tab.id}
                    role='tab'
                    aria-selected={activeWeekPeriodTab === tab.id}
                    onClick={() => setActiveWeekPeriodTab(tab.id)}
                    $isActive={activeWeekPeriodTab === tab.id}
                  >
                    {tab.label}
                  </TabButtonStyled>
                ))}
              </WeekPeriodTabsStyled>
            </WeekTopBlockRowStyled>
            <WeekTopBlockRowStyled>
              {/* 
             Will be added at stage 2
              <ButtonStyled>
                <NoteIconStyled
                  src={noteIcon}
                  alt=''
                />
                {I18n.t('payroll.notes')}
                <span>0</span>
              </ButtonStyled>
               */}
              <ClaimedStatusStyled onMouseEnter={handleMouseEnter}>
                <SparkleIconStyled key={animationKey} />
                <ClaimedStatusBlockStyled>
                  <ClaimedTextStyled>
                    {I18n.t('payroll.you_saved')}
                  </ClaimedTextStyled>
                  <RollingNumber value={claimedAmount} />
                </ClaimedStatusBlockStyled>
              </ClaimedStatusStyled>

              <ButtonStyled
                $isOrange={numberOfConflicts > 0}
                onClick={() => numberOfConflicts > 0 && setShowConflictModal(true)}
                disabled={numberOfConflicts === 0}
                style={{
                  cursor: numberOfConflicts === 0 ? 'not-allowed' : 'pointer',
                  opacity: numberOfConflicts === 0 ? 0.6 : 1
                }}
              >
                <ClockIconStyled />
                {I18n.t('payroll.conflicting_shifts')}
                <span>{numberOfConflicts}</span>
              </ButtonStyled>
            </WeekTopBlockRowStyled>
          </WeekTopBlockStyled>
          <WeekDaysStyled>
            {renderWeekDays({
              activeWeekPeriodTab,
              filteredPeriodData,
              staticWeekDays
            })}
          </WeekDaysStyled>
        </WeekBlockStyled>
      </WeekBlockWrapStyled>

      {/* Debug block removed after fix */}
      <HoursTable
        employeesArray={employeesArray.map((employee, index) => ({
          id: index + 1,
          name: employee.name || '',
          surname: employee.surname || '',
          avatar: employee.avatar || '',
          userId: employee.userId || '',
          uid: employee.uid || '',
          positions: employee.positions || [],
          payrollId: employee.payrollId,
          customId: employee.customId
        }))}
        employeesByRole={employeesByRole}
        searchEmployee={searchTerm}
        onSearchEmployee={onSearchEmployee}
        displayBy={displayBy}
        setDisplayBy={setDisplayBy}
        displayByArray={displayByArray}
        attendanceData={filteredAttendanceData}
        currentCompany={currentCompany}
        isDataLoaded={isDataLoaded}
        selectedPositionId={selectedPositionId}
        setSelectedPositionId={_setSelectedPositionId}
        startOfPeriod={filteredPeriodData.startOfPeriod}
        currentPeriodOffset={currentPeriodOffset}
        payrollLength={filteredPeriodData.payrollLength}
        onSave={onSave}
        onDeleteShift={onDeleteShift}
        departmentRoles={departmentRoles}
        roleFilterState={roleFilterState}
        onRoleFilterChange={handleRoleFilterChange}
        isSingleRole={isSingleRole}
        singleRoleInfo={singleRoleInfo}
        hasPayrollIntegration={hasPayrollIntegration}
      />

      {/* Keep old modal as fallback for now */}
      {false && (
        <PayrollConflictModal
          key={showConflictModal ? 'open' : 'closed'}
          showModal={showConflictModal}
          onClose={() => setShowConflictModal(false)}
          conflicts={cloneDeep(allConflictingShifts)}
          roundingTime={15}
          companyId={currentCompany?.key || ''}
          jobs={currentCompany?.jobs || {}}
        />
      )}

      <ConflictShiftModal
        show={showConflictModal}
        onHide={() => setShowConflictModal(false)}
        conflicts={convertConflictDataToModalFormat(
          conflictActions?.getUnresolvedConflicts() || [],
          // Create employee lookup from employeesArray
          employeesArray.reduce((acc, emp) => {
            acc[emp.uid] = { name: emp.name || '', surname: emp.surname || '' }
            return acc
          }, {} as { [key: string]: { name: string; surname: string } })
        )}
        onResolveConflict={conflictActions?.resolveConflict}
        attendanceSettings={attendanceSettings}
      />
    </>
  )
}

export default Hours
