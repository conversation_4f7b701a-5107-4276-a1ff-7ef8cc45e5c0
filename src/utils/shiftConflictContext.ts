import { ConflictData } from 'contexts/ShiftConflictContext'

import { IAllConflictingAttendanceShifts } from 'types/attendance'

// Types for conflict modal compatibility
interface ConflictShift {
  id: string
  employeeId: string
  employeeName: string
  date: string
  type: ConflictType
  shiftData: any
  resolved?: boolean
}

type ConflictType =
  | 'clocked-in-early'
  | 'missing-end'
  | 'clocked-out-late'
  | 'unplanned-shift'
  | 'shift-too-short'
  | 'shift-under-3-hours'

/**
 * Convert the legacy conflicting shifts format to the new ConflictData format
 * used by ShiftConflictContext
 */
export const convertLegacyConflictsToContextFormat = (
  allConflictingShifts: IAllConflictingAttendanceShifts
): ConflictData[] => {
  const conflicts: ConflictData[] = []

  Object.entries(allConflictingShifts).forEach(([date, employeeShifts]) => {
    Object.entries(employeeShifts).forEach(([employeeId, shifts]) => {
      Object.entries(shifts).forEach(([shiftKey, shift]) => {
        // Determine conflict type based on shift properties
        let conflictType: ConflictData['type'] = 'clock_in_different'

        if (shift.neverClockedOut) {
          conflictType = 'never_clocked_out'
        } else if (shift.missingPosition) {
          conflictType = 'missing_position'
        } else if (shift.isClockOutDiffrent) {
          conflictType = 'clock_out_different'
        } else if (shift.isClockInDifferent) {
          conflictType = 'clock_in_different'
        }

        const conflictData: ConflictData = {
          id: `${date}-${employeeId}-${shiftKey}`,
          date,
          employeeId,
          shiftKey,
          shift,
          type: conflictType,
          isResolved: false
        }

        conflicts.push(conflictData)
      })
    })
  })

  return conflicts
}

/**
 * Convert ConflictData to ConflictShift format for the modal
 */
export const convertConflictDataToModalFormat = (
  conflicts: ConflictData[],
  employees: { [employeeId: string]: { name: string; surname: string } } = {}
): ConflictShift[] => {
  return conflicts.map(conflict => {
    const employee = employees[conflict.employeeId]
    const employeeName = employee
      ? `${employee.name} ${employee.surname}`.trim()
      : 'Unknown Employee'

    // Map our conflict types to modal types
    let modalType: ConflictType = 'unplanned-shift'

    switch (conflict.type) {
      case 'clock_in_different':
        modalType = 'clocked-in-early'
        break
      case 'clock_out_different':
        modalType = 'clocked-out-late'
        break
      case 'never_clocked_out':
        modalType = 'missing-end'
        break
      case 'missing_position':
        modalType = 'unplanned-shift'
        break
      default:
        modalType = 'unplanned-shift'
    }

    return {
      id: conflict.id,
      employeeId: conflict.employeeId,
      employeeName,
      date: conflict.date,
      type: modalType,
      shiftData: conflict.shift,
      resolved: conflict.isResolved
    }
  })
}

/**
 * Group conflicts by type for display purposes
 */
export const groupConflictsByType = (
  conflicts: ConflictData[]
): Record<string, ConflictData[]> => {
  const grouped: Record<string, ConflictData[]> = {}

  conflicts.forEach(conflict => {
    const type = conflict.type || 'unknown'
    if (!grouped[type]) {
      grouped[type] = []
    }
    grouped[type].push(conflict)
  })

  return grouped
}

/**
 * Get conflict type display information
 */
export const getConflictTypeInfo = (type: ConflictData['type']) => {
  switch (type) {
    case 'clock_in_different':
      return {
        label: 'Clock In Different',
        description: 'Employee clocked in at a different time than scheduled',
        color: '#ff9500'
      }
    case 'clock_out_different':
      return {
        label: 'Clock Out Different',
        description: 'Employee clocked out at a different time than scheduled',
        color: '#ff6b35'
      }
    case 'never_clocked_out':
      return {
        label: 'Never Clocked Out',
        description: 'Employee never clocked out of their shift',
        color: '#e74c3c'
      }
    case 'missing_position':
      return {
        label: 'Missing Position',
        description: 'Shift is missing position information',
        color: '#9b59b6'
      }
    default:
      return {
        label: 'Unknown',
        description: 'Unknown conflict type',
        color: '#95a5a6'
      }
  }
}
